using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Core.Extensions;

/// <summary>
/// Extension methods for StorageTier enum.
/// </summary>
public static class StorageTierExtensions
{
    /// <summary>
    /// Gets the display name for the storage tier.
    /// </summary>
    public static string GetDisplayName(this StorageTier tier)
    {
        return tier switch
        {
            StorageTier.Hot => "Hot (0-90 days)",
            StorageTier.Warm => "Warm (90-365 days)",
            StorageTier.Cold => "Cold (365+ days)",
            _ => tier.ToString()
        };
    }

    /// <summary>
    /// Gets the expected performance characteristics for the tier.
    /// </summary>
    public static string GetPerformanceProfile(this StorageTier tier)
    {
        return tier switch
        {
            StorageTier.Hot => "High performance, low latency (< 50ms)",
            StorageTier.Warm => "Balanced performance (< 200ms)",
            StorageTier.Cold => "Cost-optimized, higher latency (< 1000ms)",
            _ => "Unknown performance profile"
        };
    }

    /// <summary>
    /// Gets the storage technology used for the tier.
    /// </summary>
    public static string GetStorageTechnology(this StorageTier tier)
    {
        return tier switch
        {
            StorageTier.Hot => "SQL Server with SSD storage",
            StorageTier.Warm => "SQL Server with compression",
            StorageTier.Cold => "Azure Blob Storage",
            _ => "Unknown storage technology"
        };
    }

    /// <summary>
    /// Gets the relative cost factor for the tier (Hot = 1.0).
    /// </summary>
    public static double GetCostFactor(this StorageTier tier)
    {
        return tier switch
        {
            StorageTier.Hot => 1.0,
            StorageTier.Warm => 0.3,
            StorageTier.Cold => 0.05,
            _ => 1.0
        };
    }

    /// <summary>
    /// Determines if the tier supports real-time queries.
    /// </summary>
    public static bool SupportsRealTimeQueries(this StorageTier tier)
    {
        return tier switch
        {
            StorageTier.Hot => true,
            StorageTier.Warm => true,
            StorageTier.Cold => false,
            _ => false
        };
    }

    /// <summary>
    /// Gets the recommended cache TTL for the tier.
    /// </summary>
    public static TimeSpan GetRecommendedCacheTtl(this StorageTier tier)
    {
        return tier switch
        {
            StorageTier.Hot => TimeSpan.FromMinutes(15),
            StorageTier.Warm => TimeSpan.FromHours(1),
            StorageTier.Cold => TimeSpan.FromHours(4),
            _ => TimeSpan.FromMinutes(30)
        };
    }

    /// <summary>
    /// Gets all storage tiers ordered by performance (fastest first).
    /// </summary>
    public static IEnumerable<StorageTier> GetAllTiersOrderedByPerformance()
    {
        return new[] { StorageTier.Hot, StorageTier.Warm, StorageTier.Cold };
    }

    /// <summary>
    /// Gets all storage tiers ordered by cost (cheapest first).
    /// </summary>
    public static IEnumerable<StorageTier> GetAllTiersOrderedByCost()
    {
        return new[] { StorageTier.Cold, StorageTier.Warm, StorageTier.Hot };
    }
}

/// <summary>
/// Extension methods for HistoryQuery.
/// </summary>
public static class HistoryQueryExtensions
{
    /// <summary>
    /// Creates a cache-friendly string representation of the query.
    /// </summary>
    public static string ToCacheKey(this HistoryQuery query, string prefix = "history:")
    {
        var parts = new List<string> { prefix };

        if (query.LeadId.HasValue)
            parts.Add($"lead:{query.LeadId}");

        if (!string.IsNullOrEmpty(query.FieldName))
            parts.Add($"field:{query.FieldName}");

        if (!string.IsNullOrEmpty(query.ChangedBy))
            parts.Add($"user:{query.ChangedBy}");

        if (query.StartDate.HasValue)
            parts.Add($"start:{query.StartDate:yyyyMMdd}");

        if (query.EndDate.HasValue)
            parts.Add($"end:{query.EndDate:yyyyMMdd}");

        parts.Add($"page:{query.Page}");
        parts.Add($"size:{query.PageSize}");
        parts.Add($"sort:{query.SortOrder}");
        parts.Add($"meta:{query.IncludeMetadata}");

        return string.Join(":", parts);
    }

    /// <summary>
    /// Estimates the complexity of the query for performance optimization.
    /// </summary>
    public static QueryComplexity EstimateComplexity(this HistoryQuery query)
    {
        var score = 0;

        // Simple filters reduce complexity
        if (query.LeadId.HasValue) score -= 2;
        if (!string.IsNullOrEmpty(query.FieldName)) score -= 1;
        if (!string.IsNullOrEmpty(query.ChangedBy)) score -= 1;

        // Date ranges can increase complexity
        if (query.StartDate.HasValue && query.EndDate.HasValue)
        {
            var range = query.EndDate.Value - query.StartDate.Value;
            if (range.TotalDays > 365) score += 3;
            else if (range.TotalDays > 90) score += 2;
            else if (range.TotalDays > 30) score += 1;
        }
        else if (query.StartDate.HasValue || query.EndDate.HasValue)
        {
            score += 2; // Open-ended ranges are more complex
        }
        else
        {
            score += 4; // No date filter means scanning all data
        }

        // Large page sizes increase complexity
        if (query.PageSize > 100) score += 1;
        if (query.PageSize > 500) score += 2;

        // Complex sorting increases complexity
        if (query.SortOrder != HistorySortOrder.ChangedAtDescending) score += 1;

        return score switch
        {
            <= 0 => QueryComplexity.Simple,
            <= 3 => QueryComplexity.Moderate,
            <= 6 => QueryComplexity.Complex,
            _ => QueryComplexity.VeryComplex
        };
    }

    /// <summary>
    /// Gets the estimated query execution time based on complexity and tier.
    /// </summary>
    public static TimeSpan GetEstimatedExecutionTime(this HistoryQuery query, StorageTier tier)
    {
        var complexity = query.EstimateComplexity();
        var baseTime = tier switch
        {
            StorageTier.Hot => TimeSpan.FromMilliseconds(10),
            StorageTier.Warm => TimeSpan.FromMilliseconds(50),
            StorageTier.Cold => TimeSpan.FromMilliseconds(200),
            _ => TimeSpan.FromMilliseconds(100)
        };

        var multiplier = complexity switch
        {
            QueryComplexity.Simple => 1.0,
            QueryComplexity.Moderate => 2.0,
            QueryComplexity.Complex => 5.0,
            QueryComplexity.VeryComplex => 10.0,
            _ => 3.0
        };

        return TimeSpan.FromMilliseconds(baseTime.TotalMilliseconds * multiplier);
    }
}

/// <summary>
/// Represents the complexity level of a query.
/// </summary>
public enum QueryComplexity
{
    /// <summary>
    /// Simple query with specific filters and small result set.
    /// </summary>
    Simple = 1,

    /// <summary>
    /// Moderate complexity with some filtering.
    /// </summary>
    Moderate = 2,

    /// <summary>
    /// Complex query with broad date ranges or multiple tiers.
    /// </summary>
    Complex = 3,

    /// <summary>
    /// Very complex query that may require special handling.
    /// </summary>
    VeryComplex = 4
}
