using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Core.Configuration;

/// <summary>
/// Configuration options for the CRM history storage system.
/// </summary>
public class HistoryOptions
{
    /// <summary>
    /// Configuration section name in appsettings.json.
    /// </summary>
    public const string SectionName = "History";

    /// <summary>
    /// Number of days to retain data in the hot tier (default: 90 days).
    /// </summary>
    [Range(1, 365)]
    public int HotTierRetentionDays { get; set; } = 90;

    /// <summary>
    /// Number of days to retain data in the warm tier (default: 365 days).
    /// </summary>
    [Range(1, 3650)]
    public int WarmTierRetentionDays { get; set; } = 365;

    /// <summary>
    /// Default batch size for bulk operations (default: 1000).
    /// </summary>
    [Range(1, 10000)]
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// Cache expiration time in minutes (default: 60 minutes).
    /// </summary>
    [Range(1, 1440)]
    public int CacheExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// Maximum number of concurrent database connections per tier.
    /// </summary>
    [Range(1, 100)]
    public int MaxConcurrentConnections { get; set; } = 10;

    /// <summary>
    /// Query timeout in seconds (default: 30 seconds).
    /// </summary>
    [Range(1, 300)]
    public int QueryTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Maximum number of retry attempts for failed operations.
    /// </summary>
    [Range(0, 10)]
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Initial retry delay in milliseconds.
    /// </summary>
    [Range(100, 10000)]
    public int InitialRetryDelayMs { get; set; } = 100;

    /// <summary>
    /// Maximum retry delay in milliseconds.
    /// </summary>
    [Range(1000, 60000)]
    public int MaxRetryDelayMs { get; set; } = 5000;

    /// <summary>
    /// Connection strings for different storage tiers.
    /// </summary>
    public ConnectionStrings ConnectionStrings { get; set; } = new();

    /// <summary>
    /// Caching configuration.
    /// </summary>
    public CacheOptions Cache { get; set; } = new();

    /// <summary>
    /// Performance monitoring configuration.
    /// </summary>
    public PerformanceOptions Performance { get; set; } = new();

    /// <summary>
    /// Archival process configuration.
    /// </summary>
    public ArchivalOptions Archival { get; set; } = new();

    /// <summary>
    /// Validates the configuration options.
    /// </summary>
    public bool IsValid(out List<string> errors)
    {
        errors = new List<string>();

        if (HotTierRetentionDays >= WarmTierRetentionDays)
        {
            errors.Add("HotTierRetentionDays must be less than WarmTierRetentionDays");
        }

        if (string.IsNullOrWhiteSpace(ConnectionStrings.Hot))
        {
            errors.Add("Hot tier connection string is required");
        }

        if (string.IsNullOrWhiteSpace(ConnectionStrings.Warm))
        {
            errors.Add("Warm tier connection string is required");
        }

        if (string.IsNullOrWhiteSpace(ConnectionStrings.Cold))
        {
            errors.Add("Cold tier connection string is required");
        }

        if (InitialRetryDelayMs >= MaxRetryDelayMs)
        {
            errors.Add("InitialRetryDelayMs must be less than MaxRetryDelayMs");
        }

        return !errors.Any();
    }

    /// <summary>
    /// Gets the retention period for the specified tier.
    /// </summary>
    public TimeSpan GetRetentionPeriod(StorageTier tier)
    {
        return tier switch
        {
            StorageTier.Hot => TimeSpan.FromDays(HotTierRetentionDays),
            StorageTier.Warm => TimeSpan.FromDays(WarmTierRetentionDays),
            StorageTier.Cold => TimeSpan.MaxValue, // Cold tier keeps data indefinitely
            _ => throw new ArgumentException($"Unknown storage tier: {tier}")
        };
    }

    /// <summary>
    /// Gets the connection string for the specified tier.
    /// </summary>
    public string GetConnectionString(StorageTier tier)
    {
        return tier switch
        {
            StorageTier.Hot => ConnectionStrings.Hot,
            StorageTier.Warm => ConnectionStrings.Warm,
            StorageTier.Cold => ConnectionStrings.Cold,
            _ => throw new ArgumentException($"Unknown storage tier: {tier}")
        };
    }
}

/// <summary>
/// Connection strings for different storage tiers.
/// </summary>
public class ConnectionStrings
{
    /// <summary>
    /// Connection string for hot tier storage (SQL Server).
    /// </summary>
    [Required]
    public string Hot { get; set; } = string.Empty;

    /// <summary>
    /// Connection string for warm tier storage (SQL Server with compression).
    /// </summary>
    [Required]
    public string Warm { get; set; } = string.Empty;

    /// <summary>
    /// Connection string for cold tier storage (Azure Blob Storage).
    /// </summary>
    [Required]
    public string Cold { get; set; } = string.Empty;

    /// <summary>
    /// Connection string for Redis cache.
    /// </summary>
    public string? Redis { get; set; }
}

/// <summary>
/// Caching configuration options.
/// </summary>
public class CacheOptions
{
    /// <summary>
    /// Whether caching is enabled (default: true).
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Default cache expiration time in minutes.
    /// </summary>
    [Range(1, 1440)]
    public int DefaultExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// Maximum number of items to cache.
    /// </summary>
    [Range(100, 100000)]
    public int MaxCacheSize { get; set; } = 10000;

    /// <summary>
    /// Cache key prefix to avoid collisions.
    /// </summary>
    public string KeyPrefix { get; set; } = "crm_history:";

    /// <summary>
    /// Whether to use distributed cache (Redis) or in-memory cache.
    /// </summary>
    public bool UseDistributedCache { get; set; } = true;
}

/// <summary>
/// Performance monitoring configuration.
/// </summary>
public class PerformanceOptions
{
    /// <summary>
    /// Whether performance monitoring is enabled.
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Slow query threshold in milliseconds.
    /// </summary>
    [Range(1, 10000)]
    public int SlowQueryThresholdMs { get; set; } = 1000;

    /// <summary>
    /// Whether to log slow queries.
    /// </summary>
    public bool LogSlowQueries { get; set; } = true;

    /// <summary>
    /// Whether to collect detailed execution statistics.
    /// </summary>
    public bool CollectDetailedStats { get; set; } = false;

    /// <summary>
    /// Sample rate for performance metrics (0.0 to 1.0).
    /// </summary>
    [Range(0.0, 1.0)]
    public double MetricsSampleRate { get; set; } = 0.1;
}

/// <summary>
/// Archival process configuration.
/// </summary>
public class ArchivalOptions
{
    /// <summary>
    /// Whether automatic archival is enabled.
    /// </summary>
    public bool AutoArchivalEnabled { get; set; } = true;

    /// <summary>
    /// Cron expression for archival schedule (default: daily at 2 AM).
    /// </summary>
    public string ArchivalSchedule { get; set; } = "0 2 * * *";

    /// <summary>
    /// Number of records to process in each archival batch.
    /// </summary>
    [Range(100, 100000)]
    public int ArchivalBatchSize { get; set; } = 10000;

    /// <summary>
    /// Maximum time to spend on archival in minutes.
    /// </summary>
    [Range(1, 480)]
    public int MaxArchivalTimeMinutes { get; set; } = 60;

    /// <summary>
    /// Whether to verify data integrity after archival.
    /// </summary>
    public bool VerifyAfterArchival { get; set; } = true;

    /// <summary>
    /// Number of days to keep archival logs.
    /// </summary>
    [Range(1, 365)]
    public int ArchivalLogRetentionDays { get; set; } = 30;
}
