using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Core.Models;

/// <summary>
/// Represents a batch of history entries for bulk operations.
/// Optimized for high-throughput scenarios with validation and error handling.
/// </summary>
public class HistoryBatch
{
    /// <summary>
    /// Unique identifier for the batch operation.
    /// </summary>
    public Guid BatchId { get; set; } = Guid.NewGuid();

    /// <summary>
    /// The history entries in this batch.
    /// Limited to 1000 entries per batch for optimal performance.
    /// </summary>
    [Required]
    public IList<HistoryEntry> Entries { get; set; } = new List<HistoryEntry>();

    /// <summary>
    /// Timestamp when the batch was created.
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// User or system that created this batch.
    /// </summary>
    [Required]
    [StringLength(100)]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// Optional metadata for the batch operation.
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Maximum number of entries allowed per batch.
    /// </summary>
    public const int MaxBatchSize = 1000;

    /// <summary>
    /// Current number of entries in the batch.
    /// </summary>
    public int Count => Entries.Count;

    /// <summary>
    /// Indicates whether the batch is at maximum capacity.
    /// </summary>
    public bool IsFull => Count >= MaxBatchSize;

    /// <summary>
    /// Indicates whether the batch is empty.
    /// </summary>
    public bool IsEmpty => Count == 0;

    /// <summary>
    /// Validates all entries in the batch.
    /// </summary>
    public BatchValidationResult Validate()
    {
        var result = new BatchValidationResult { BatchId = BatchId };

        if (IsEmpty)
        {
            result.AddError("Batch cannot be empty");
            return result;
        }

        if (Count > MaxBatchSize)
        {
            result.AddError($"Batch size ({Count}) exceeds maximum allowed ({MaxBatchSize})");
        }

        if (string.IsNullOrWhiteSpace(CreatedBy))
        {
            result.AddError("CreatedBy is required");
        }

        // Validate individual entries
        for (int i = 0; i < Entries.Count; i++)
        {
            var entry = Entries[i];
            if (!entry.IsValid())
            {
                result.AddError($"Entry at index {i} is invalid: {entry}");
            }
        }

        // Check for duplicate entries within the batch
        var duplicates = Entries
            .GroupBy(e => new { e.LeadId, e.FieldName, e.ChangedAt, e.ChangedBy })
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        foreach (var duplicate in duplicates)
        {
            result.AddWarning($"Duplicate entry found: LeadId={duplicate.LeadId}, FieldName={duplicate.FieldName}, ChangedAt={duplicate.ChangedAt}");
        }

        return result;
    }

    /// <summary>
    /// Adds a history entry to the batch.
    /// </summary>
    public bool TryAddEntry(HistoryEntry entry)
    {
        if (IsFull || entry == null || !entry.IsValid())
            return false;

        Entries.Add(entry);
        return true;
    }

    /// <summary>
    /// Adds multiple history entries to the batch.
    /// </summary>
    public int AddEntries(IEnumerable<HistoryEntry> entries)
    {
        int added = 0;
        foreach (var entry in entries)
        {
            if (!TryAddEntry(entry))
                break;
            added++;
        }
        return added;
    }

    /// <summary>
    /// Groups entries by storage tier for efficient processing.
    /// </summary>
    public Dictionary<StorageTier, List<HistoryEntry>> GroupByTier()
    {
        return Entries
            .GroupBy(e => e.GetStorageTier())
            .ToDictionary(g => g.Key, g => g.ToList());
    }

    /// <summary>
    /// Splits the batch into smaller batches of the specified size.
    /// </summary>
    public IEnumerable<HistoryBatch> Split(int batchSize = MaxBatchSize)
    {
        if (batchSize <= 0 || batchSize > MaxBatchSize)
            batchSize = MaxBatchSize;

        var batches = new List<HistoryBatch>();
        
        for (int i = 0; i < Entries.Count; i += batchSize)
        {
            var batchEntries = Entries.Skip(i).Take(batchSize).ToList();
            var batch = new HistoryBatch
            {
                BatchId = Guid.NewGuid(),
                Entries = batchEntries,
                CreatedAt = CreatedAt,
                CreatedBy = CreatedBy,
                Metadata = $"Split from batch {BatchId}, part {(i / batchSize) + 1}"
            };
            batches.Add(batch);
        }

        return batches;
    }

    /// <summary>
    /// Creates a batch from a collection of history entries.
    /// </summary>
    public static HistoryBatch Create(IEnumerable<HistoryEntry> entries, string createdBy, string? metadata = null)
    {
        var batch = new HistoryBatch
        {
            CreatedBy = createdBy,
            Metadata = metadata
        };

        batch.AddEntries(entries);
        return batch;
    }

    public override string ToString()
    {
        return $"HistoryBatch[{BatchId}]: {Count} entries, created by {CreatedBy} at {CreatedAt:yyyy-MM-dd HH:mm:ss}";
    }
}

/// <summary>
/// Represents the result of batch validation.
/// </summary>
public class BatchValidationResult
{
    /// <summary>
    /// The batch ID that was validated.
    /// </summary>
    public Guid BatchId { get; set; }

    /// <summary>
    /// List of validation errors.
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// List of validation warnings.
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Indicates whether the batch passed validation.
    /// </summary>
    public bool IsValid => !Errors.Any();

    /// <summary>
    /// Indicates whether there are any warnings.
    /// </summary>
    public bool HasWarnings => Warnings.Any();

    /// <summary>
    /// Adds an error to the validation result.
    /// </summary>
    public void AddError(string error)
    {
        Errors.Add(error);
    }

    /// <summary>
    /// Adds a warning to the validation result.
    /// </summary>
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }

    /// <summary>
    /// Gets a summary of all validation issues.
    /// </summary>
    public string GetSummary()
    {
        var parts = new List<string>();
        
        if (Errors.Any())
            parts.Add($"{Errors.Count} error(s)");
        
        if (Warnings.Any())
            parts.Add($"{Warnings.Count} warning(s)");

        return parts.Any() ? string.Join(", ", parts) : "No issues";
    }

    public override string ToString()
    {
        return $"BatchValidationResult[{BatchId}]: {GetSummary()}";
    }
}
