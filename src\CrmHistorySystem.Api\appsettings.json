{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/crm-history-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*", "ConnectionStrings": {"Hot": "Server=(localdb)\\mssqllocaldb;Database=CrmHistory_Hot;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "Warm": "Server=(localdb)\\mssqllocaldb;Database=CrmHistory_Warm;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "Cold": "DefaultEndpointsProtocol=https;AccountName=your_storage_account;AccountKey=your_account_key;EndpointSuffix=core.windows.net", "Redis": "localhost:6379"}, "History": {"HotTierRetentionDays": 90, "WarmTierRetentionDays": 365, "BatchSize": 1000, "CacheExpirationMinutes": 60, "MaxConcurrentConnections": 10, "QueryTimeoutSeconds": 30, "MaxRetryAttempts": 3, "InitialRetryDelayMs": 100, "MaxRetryDelayMs": 5000, "Cache": {"Enabled": true, "DefaultExpirationMinutes": 60, "MaxCacheSize": 10000, "KeyPrefix": "crm_history:", "UseDistributedCache": true}, "Performance": {"Enabled": true, "SlowQueryThresholdMs": 1000, "LogSlowQueries": true, "CollectDetailedStats": false, "MetricsSampleRate": 0.1}, "Archival": {"AutoArchivalEnabled": true, "ArchivalSchedule": "0 2 * * *", "ArchivalBatchSize": 10000, "MaxArchivalTimeMinutes": 60, "VerifyAfterArchival": true, "ArchivalLogRetentionDays": 30}}}