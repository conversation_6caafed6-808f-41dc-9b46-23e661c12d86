# CRM History System Demo Runner
Write-Host "🚀 CRM History System Demo" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Create project directory
$projectDir = "$env:USERPROFILE\Desktop\CrmHistoryDemo"
Write-Host "📁 Creating project directory: $projectDir" -ForegroundColor Yellow

if (Test-Path $projectDir) {
    Remove-Item $projectDir -Recurse -Force
}
New-Item -ItemType Directory -Path $projectDir -Force | Out-Null

# Create project file
$csprojContent = @"
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="7.0.11" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="7.0.11" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>
</Project>
"@

$csprojContent | Out-File -FilePath "$projectDir\CrmHistoryDemo.csproj" -Encoding UTF8

# Create Program.cs
$programContent = @"
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Supabase connection
var connectionString = "Host=db.ezcwxlffzmqwdzpqxtan.supabase.co;Database=********;Username=********;Password=********;Port=5432;SSL Mode=Require;Trust Server Certificate=true";
builder.Services.AddDbContext<CrmDbContext>(options => options.UseNpgsql(connectionString));

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c => { c.RoutePrefix = string.Empty; });
}

app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();

// Initialize database
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<CrmDbContext>();
    try
    {
        await context.Database.EnsureCreatedAsync();
        if (!await context.Leads.AnyAsync())
        {
            var sampleLeads = new[]
            {
                new Lead { Name = "John Doe", ContactNo = "**********", Email = "<EMAIL>", Notes = "Interested in premium properties", ChosenProject = "Luxury Apartments", AssignTo = Guid.NewGuid(), CreatedBy = "system", UpdatedBy = "system" },
                new Lead { Name = "Jane Smith", ContactNo = "**********", Email = "<EMAIL>", Notes = "Looking for 3BHK apartment", ChosenProject = "Premium Villas", AssignTo = Guid.NewGuid(), CreatedBy = "system", UpdatedBy = "system" }
            };
            context.Leads.AddRange(sampleLeads);
            await context.SaveChangesAsync();
            
            foreach (var lead in sampleLeads)
            {
                var historyEntries = new[]
                {
                    new HistoryEntry { LeadId = lead.Id, FieldName = "Name", OldValue = null, NewValue = lead.Name, ChangedBy = "system", Tier = "Hot" },
                    new HistoryEntry { LeadId = lead.Id, FieldName = "ContactNo", OldValue = null, NewValue = lead.ContactNo, ChangedBy = "system", Tier = "Hot" },
                    new HistoryEntry { LeadId = lead.Id, FieldName = "Email", OldValue = null, NewValue = lead.Email, ChangedBy = "system", Tier = "Hot" }
                };
                context.HistoryEntries.AddRange(historyEntries);
            }
            await context.SaveChangesAsync();
            Console.WriteLine("✅ Sample data created with history tracking!");
        }
    }
    catch (Exception ex) { Console.WriteLine(`$"❌ Database error: {ex.Message}"); }
}

Console.WriteLine("🚀 CRM History System Demo API is running!");
Console.WriteLine("📖 Swagger UI available at: http://localhost:5000");
app.Run();

public class Lead
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    [Required] public string ContactNo { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string? Notes { get; set; }
    public string? ChosenProject { get; set; }
    public Guid AssignTo { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public string CreatedBy { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;
}

public class HistoryEntry
{
    public long Id { get; set; }
    public int LeadId { get; set; }
    public string FieldName { get; set; } = string.Empty;
    public string? OldValue { get; set; }
    public string? NewValue { get; set; }
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
    public string ChangedBy { get; set; } = string.Empty;
    public string Tier { get; set; } = "Hot";
}

public class CrmDbContext : DbContext
{
    public CrmDbContext(DbContextOptions<CrmDbContext> options) : base(options) { }
    public DbSet<Lead> Leads { get; set; } = null!;
    public DbSet<HistoryEntry> HistoryEntries { get; set; } = null!;
}

[Microsoft.AspNetCore.Mvc.ApiController, Microsoft.AspNetCore.Mvc.Route("api/[controller]")]
public class LeadsController : Microsoft.AspNetCore.Mvc.ControllerBase
{
    private readonly CrmDbContext _context;
    public LeadsController(CrmDbContext context) => _context = context;

    [Microsoft.AspNetCore.Mvc.HttpGet]
    public async Task<IEnumerable<Lead>> GetLeads() => await _context.Leads.ToListAsync();

    [Microsoft.AspNetCore.Mvc.HttpGet("{id}/history")]
    public async Task<IEnumerable<HistoryEntry>> GetLeadHistory(int id) => 
        await _context.HistoryEntries.Where(h => h.LeadId == id).OrderByDescending(h => h.ChangedAt).ToListAsync();
}
"@

$programContent | Out-File -FilePath "$projectDir\Program.cs" -Encoding UTF8

Write-Host "📦 Restoring packages..." -ForegroundColor Yellow
Set-Location $projectDir
dotnet restore

Write-Host "🔨 Building project..." -ForegroundColor Yellow
dotnet build

Write-Host "🚀 Starting the application..." -ForegroundColor Green
Write-Host "The application will start on http://localhost:5000" -ForegroundColor Cyan
Write-Host "Swagger UI will be available at the root URL" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop the application" -ForegroundColor Yellow

dotnet run
