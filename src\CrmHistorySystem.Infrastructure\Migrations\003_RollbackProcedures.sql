-- =============================================
-- CRM History System - Rollback Procedures
-- Version: 1.0.0
-- Description: Provides safe rollback capabilities for the migration process
-- =============================================

-- Rollback state tracking
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'HistoryRollbackLog')
BEGIN
    CREATE TABLE [dbo].[HistoryRollbackLog] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [RollbackName] NVARCHAR(100) NOT NULL,
        [StartTime] DATETIME2 NOT NULL DEFAULT(GETUTCDATE()),
        [EndTime] DATETIME2 NULL,
        [Operation] NVARCHAR(50) NOT NULL, -- Backup, Restore, Cleanup
        [Status] NVARCHAR(20) NOT NULL DEFAULT('InProgress'), -- InProgress, Completed, Failed
        [RecordsAffected] BIGINT NOT NULL DEFAULT(0),
        [ErrorMessage] NVARCHAR(MAX) NULL,
        [BackupLocation] NVARCHAR(500) NULL
    );
END;

-- =============================================
-- Pre-Migration Backup Procedure
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_BackupBeforeMigration]
    @BackupTableSuffix NVARCHAR(50) = NULL, -- Optional suffix for backup tables
    @CreateBackupTables BIT = 1,
    @BackupExistingData BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    DECLARE @BackupName NVARCHAR(100) = 'PreMigration_' + ISNULL(@BackupTableSuffix, FORMAT(GETUTCDATE(), 'yyyyMMdd_HHmmss'));
    DECLARE @StartTime DATETIME2 = GETUTCDATE();
    DECLARE @RecordsBackedUp BIGINT = 0;
    
    BEGIN TRY
        -- Log backup start
        INSERT INTO [dbo].[HistoryRollbackLog] 
        ([RollbackName], [StartTime], [Operation], [Status])
        VALUES (@BackupName, @StartTime, 'Backup', 'InProgress');
        
        DECLARE @LogId INT = SCOPE_IDENTITY();
        
        PRINT 'Starting pre-migration backup: ' + @BackupName;
        PRINT 'Backup tables: ' + CASE WHEN @CreateBackupTables = 1 THEN 'YES' ELSE 'NO' END;
        PRINT 'Backup existing data: ' + CASE WHEN @BackupExistingData = 1 THEN 'YES' ELSE 'NO' END;
        PRINT '----------------------------------------';
        
        -- Create backup tables if requested
        IF @CreateBackupTables = 1
        BEGIN
            -- Backup hot tier table structure and data
            DECLARE @BackupHotTable NVARCHAR(128) = 'HistoryEntries_Hot_Backup_' + @BackupName;
            DECLARE @BackupWarmTable NVARCHAR(128) = 'HistoryEntries_Warm_Backup_' + @BackupName;
            
            -- Check if hot tier table exists and has data
            IF EXISTS (SELECT * FROM sys.tables WHERE name = 'HistoryEntries_Hot')
            BEGIN
                DECLARE @HotCount BIGINT;
                SELECT @HotCount = COUNT(*) FROM [dbo].[HistoryEntries_Hot];
                
                IF @HotCount > 0 AND @BackupExistingData = 1
                BEGIN
                    EXEC('SELECT * INTO [dbo].[' + @BackupHotTable + '] FROM [dbo].[HistoryEntries_Hot]');
                    SET @RecordsBackedUp = @RecordsBackedUp + @HotCount;
                    PRINT 'Backed up ' + CAST(@HotCount AS NVARCHAR(20)) + ' records from hot tier to ' + @BackupHotTable;
                END
                ELSE
                BEGIN
                    EXEC('SELECT TOP 0 * INTO [dbo].[' + @BackupHotTable + '] FROM [dbo].[HistoryEntries_Hot]');
                    PRINT 'Created empty backup table: ' + @BackupHotTable;
                END
            END
            
            -- Check if warm tier table exists and has data
            IF EXISTS (SELECT * FROM sys.tables WHERE name = 'HistoryEntries_Warm')
            BEGIN
                DECLARE @WarmCount BIGINT;
                SELECT @WarmCount = COUNT(*) FROM [dbo].[HistoryEntries_Warm];
                
                IF @WarmCount > 0 AND @BackupExistingData = 1
                BEGIN
                    EXEC('SELECT * INTO [dbo].[' + @BackupWarmTable + '] FROM [dbo].[HistoryEntries_Warm]');
                    SET @RecordsBackedUp = @RecordsBackedUp + @WarmCount;
                    PRINT 'Backed up ' + CAST(@WarmCount AS NVARCHAR(20)) + ' records from warm tier to ' + @BackupWarmTable;
                END
                ELSE
                BEGIN
                    EXEC('SELECT TOP 0 * INTO [dbo].[' + @BackupWarmTable + '] FROM [dbo].[HistoryEntries_Warm]');
                    PRINT 'Created empty backup table: ' + @BackupWarmTable;
                END
            END
        END
        
        -- Update backup log
        UPDATE [dbo].[HistoryRollbackLog] 
        SET [EndTime] = GETUTCDATE(),
            [Status] = 'Completed',
            [RecordsAffected] = @RecordsBackedUp,
            [BackupLocation] = @BackupHotTable + ', ' + @BackupWarmTable
        WHERE [Id] = @LogId;
        
        PRINT '----------------------------------------';
        PRINT 'Backup completed successfully!';
        PRINT 'Total records backed up: ' + CAST(@RecordsBackedUp AS NVARCHAR(20));
        PRINT 'Backup name: ' + @BackupName;
        
    END TRY
    BEGIN CATCH
        -- Update backup log with error
        UPDATE [dbo].[HistoryRollbackLog] 
        SET [EndTime] = GETUTCDATE(),
            [Status] = 'Failed',
            [ErrorMessage] = ERROR_MESSAGE()
        WHERE [Id] = @LogId;
        
        PRINT 'Backup failed: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;

-- =============================================
-- Rollback Migration Procedure
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_RollbackMigration]
    @BackupName NVARCHAR(100),
    @RestoreData BIT = 1,
    @DropNewTables BIT = 0,
    @ConfirmRollback NVARCHAR(10) = 'NO' -- Must be 'YES' to proceed
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    -- Safety check
    IF @ConfirmRollback != 'YES'
    BEGIN
        PRINT 'ROLLBACK CANCELLED: You must set @ConfirmRollback = ''YES'' to proceed with rollback.';
        PRINT 'This operation will restore data from backup and potentially drop new tables.';
        RETURN;
    END
    
    DECLARE @StartTime DATETIME2 = GETUTCDATE();
    DECLARE @RecordsRestored BIGINT = 0;
    DECLARE @RollbackName NVARCHAR(100) = 'Rollback_' + @BackupName + '_' + FORMAT(GETUTCDATE(), 'yyyyMMdd_HHmmss');
    
    BEGIN TRY
        -- Log rollback start
        INSERT INTO [dbo].[HistoryRollbackLog] 
        ([RollbackName], [StartTime], [Operation], [Status])
        VALUES (@RollbackName, @StartTime, 'Restore', 'InProgress');
        
        DECLARE @LogId INT = SCOPE_IDENTITY();
        
        PRINT 'Starting rollback operation: ' + @RollbackName;
        PRINT 'Source backup: ' + @BackupName;
        PRINT 'Restore data: ' + CASE WHEN @RestoreData = 1 THEN 'YES' ELSE 'NO' END;
        PRINT 'Drop new tables: ' + CASE WHEN @DropNewTables = 1 THEN 'YES' ELSE 'NO' END;
        PRINT '----------------------------------------';
        
        -- Verify backup tables exist
        DECLARE @BackupHotTable NVARCHAR(128) = 'HistoryEntries_Hot_Backup_' + @BackupName;
        DECLARE @BackupWarmTable NVARCHAR(128) = 'HistoryEntries_Warm_Backup_' + @BackupName;
        
        IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = @BackupHotTable)
        BEGIN
            RAISERROR('Backup table not found: %s', 16, 1, @BackupHotTable);
        END
        
        IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = @BackupWarmTable)
        BEGIN
            RAISERROR('Backup table not found: %s', 16, 1, @BackupWarmTable);
        END
        
        -- Begin rollback transaction
        BEGIN TRANSACTION;
        
        -- Clear current data if restoring
        IF @RestoreData = 1
        BEGIN
            -- Clear hot tier
            IF EXISTS (SELECT * FROM sys.tables WHERE name = 'HistoryEntries_Hot')
            BEGIN
                TRUNCATE TABLE [dbo].[HistoryEntries_Hot];
                PRINT 'Cleared hot tier table';
            END
            
            -- Clear warm tier
            IF EXISTS (SELECT * FROM sys.tables WHERE name = 'HistoryEntries_Warm')
            BEGIN
                TRUNCATE TABLE [dbo].[HistoryEntries_Warm];
                PRINT 'Cleared warm tier table';
            END
            
            -- Restore data from backup
            DECLARE @RestoreSQL NVARCHAR(MAX);
            
            -- Restore hot tier data
            SET @RestoreSQL = 'INSERT INTO [dbo].[HistoryEntries_Hot] SELECT * FROM [dbo].[' + @BackupHotTable + ']';
            EXEC sp_executesql @RestoreSQL;
            
            DECLARE @HotRestored BIGINT = @@ROWCOUNT;
            SET @RecordsRestored = @RecordsRestored + @HotRestored;
            PRINT 'Restored ' + CAST(@HotRestored AS NVARCHAR(20)) + ' records to hot tier';
            
            -- Restore warm tier data
            SET @RestoreSQL = 'INSERT INTO [dbo].[HistoryEntries_Warm] SELECT * FROM [dbo].[' + @BackupWarmTable + ']';
            EXEC sp_executesql @RestoreSQL;
            
            DECLARE @WarmRestored BIGINT = @@ROWCOUNT;
            SET @RecordsRestored = @RecordsRestored + @WarmRestored;
            PRINT 'Restored ' + CAST(@WarmRestored AS NVARCHAR(20)) + ' records to warm tier';
        END
        
        -- Drop new tables if requested
        IF @DropNewTables = 1
        BEGIN
            -- This is a destructive operation - use with extreme caution
            PRINT 'WARNING: Dropping new tables is not implemented for safety reasons.';
            PRINT 'If you need to drop tables, do so manually after verifying the rollback.';
        END
        
        COMMIT TRANSACTION;
        
        -- Update rollback log
        UPDATE [dbo].[HistoryRollbackLog] 
        SET [EndTime] = GETUTCDATE(),
            [Status] = 'Completed',
            [RecordsAffected] = @RecordsRestored
        WHERE [Id] = @LogId;
        
        PRINT '----------------------------------------';
        PRINT 'Rollback completed successfully!';
        PRINT 'Total records restored: ' + CAST(@RecordsRestored AS NVARCHAR(20));
        PRINT 'Rollback name: ' + @RollbackName;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        -- Update rollback log with error
        UPDATE [dbo].[HistoryRollbackLog] 
        SET [EndTime] = GETUTCDATE(),
            [Status] = 'Failed',
            [ErrorMessage] = ERROR_MESSAGE()
        WHERE [Id] = @LogId;
        
        PRINT 'Rollback failed: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;

-- =============================================
-- Cleanup Backup Tables Procedure
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_CleanupBackupTables]
    @BackupName NVARCHAR(100) = NULL, -- Specific backup to clean, or NULL for all old backups
    @OlderThanDays INT = 30, -- Clean backups older than this many days
    @ConfirmCleanup NVARCHAR(10) = 'NO' -- Must be 'YES' to proceed
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Safety check
    IF @ConfirmCleanup != 'YES'
    BEGIN
        PRINT 'CLEANUP CANCELLED: You must set @ConfirmCleanup = ''YES'' to proceed with cleanup.';
        PRINT 'This operation will permanently delete backup tables.';
        RETURN;
    END
    
    DECLARE @StartTime DATETIME2 = GETUTCDATE();
    DECLARE @TablesDropped INT = 0;
    DECLARE @CleanupName NVARCHAR(100) = 'Cleanup_' + FORMAT(GETUTCDATE(), 'yyyyMMdd_HHmmss');
    
    BEGIN TRY
        -- Log cleanup start
        INSERT INTO [dbo].[HistoryRollbackLog] 
        ([RollbackName], [StartTime], [Operation], [Status])
        VALUES (@CleanupName, @StartTime, 'Cleanup', 'InProgress');
        
        DECLARE @LogId INT = SCOPE_IDENTITY();
        
        PRINT 'Starting backup cleanup: ' + @CleanupName;
        PRINT 'Target backup: ' + ISNULL(@BackupName, 'All backups older than ' + CAST(@OlderThanDays AS NVARCHAR(10)) + ' days');
        PRINT '----------------------------------------';
        
        -- Find backup tables to clean
        DECLARE @TableName NVARCHAR(128);
        DECLARE @DropSQL NVARCHAR(MAX);
        
        DECLARE backup_cursor CURSOR FOR
        SELECT t.name
        FROM sys.tables t
        WHERE (t.name LIKE 'HistoryEntries_Hot_Backup_%' OR t.name LIKE 'HistoryEntries_Warm_Backup_%')
          AND (
              @BackupName IS NOT NULL AND t.name LIKE '%' + @BackupName + '%'
              OR
              @BackupName IS NULL AND t.create_date < DATEADD(day, -@OlderThanDays, GETUTCDATE())
          );
        
        OPEN backup_cursor;
        FETCH NEXT FROM backup_cursor INTO @TableName;
        
        WHILE @@FETCH_STATUS = 0
        BEGIN
            SET @DropSQL = 'DROP TABLE [dbo].[' + @TableName + ']';
            EXEC sp_executesql @DropSQL;
            
            SET @TablesDropped = @TablesDropped + 1;
            PRINT 'Dropped backup table: ' + @TableName;
            
            FETCH NEXT FROM backup_cursor INTO @TableName;
        END
        
        CLOSE backup_cursor;
        DEALLOCATE backup_cursor;
        
        -- Update cleanup log
        UPDATE [dbo].[HistoryRollbackLog] 
        SET [EndTime] = GETUTCDATE(),
            [Status] = 'Completed',
            [RecordsAffected] = @TablesDropped
        WHERE [Id] = @LogId;
        
        PRINT '----------------------------------------';
        PRINT 'Cleanup completed successfully!';
        PRINT 'Tables dropped: ' + CAST(@TablesDropped AS NVARCHAR(10));
        
    END TRY
    BEGIN CATCH
        IF CURSOR_STATUS('global', 'backup_cursor') >= 0
        BEGIN
            CLOSE backup_cursor;
            DEALLOCATE backup_cursor;
        END
        
        -- Update cleanup log with error
        UPDATE [dbo].[HistoryRollbackLog] 
        SET [EndTime] = GETUTCDATE(),
            [Status] = 'Failed',
            [ErrorMessage] = ERROR_MESSAGE()
        WHERE [Id] = @LogId;
        
        PRINT 'Cleanup failed: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;

-- =============================================
-- View Rollback History
-- =============================================

CREATE OR ALTER VIEW [dbo].[vw_RollbackHistory]
AS
SELECT 
    [Id],
    [RollbackName],
    [StartTime],
    [EndTime],
    [Operation],
    [Status],
    [RecordsAffected],
    [ErrorMessage],
    [BackupLocation],
    DATEDIFF(SECOND, [StartTime], ISNULL([EndTime], GETUTCDATE())) as DurationSeconds
FROM [dbo].[HistoryRollbackLog];

-- =============================================

PRINT 'Rollback procedures created successfully:';
PRINT '  - sp_BackupBeforeMigration: Creates backup before migration';
PRINT '  - sp_RollbackMigration: Restores data from backup';
PRINT '  - sp_CleanupBackupTables: Removes old backup tables';
PRINT '  - vw_RollbackHistory: View rollback operation history';
PRINT '';
PRINT 'Usage examples:';
PRINT '  -- Create backup before migration:';
PRINT '  EXEC sp_BackupBeforeMigration;';
PRINT '';
PRINT '  -- Rollback to backup (DANGEROUS - requires confirmation):';
PRINT '  EXEC sp_RollbackMigration @BackupName = ''PreMigration_20231201_120000'', @ConfirmRollback = ''YES'';';
PRINT '';
PRINT '  -- Clean old backups (DANGEROUS - requires confirmation):';
PRINT '  EXEC sp_CleanupBackupTables @OlderThanDays = 30, @ConfirmCleanup = ''YES'';';
