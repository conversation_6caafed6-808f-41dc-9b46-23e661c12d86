namespace CrmHistorySystem.Core.Models;

/// <summary>
/// Represents the result of a batch operation.
/// </summary>
public class BatchOperationResult
{
    /// <summary>
    /// The batch ID that was processed.
    /// </summary>
    public Guid BatchId { get; set; }

    /// <summary>
    /// Number of entries successfully processed.
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// Number of entries that failed to process.
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// Total number of entries in the batch.
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Time taken to process the batch in milliseconds.
    /// </summary>
    public long ProcessingTimeMs { get; set; }

    /// <summary>
    /// List of errors that occurred during processing.
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// List of warnings generated during processing.
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// Indicates whether the batch was fully successful.
    /// </summary>
    public bool IsSuccess => FailureCount == 0 && !Errors.Any();

    /// <summary>
    /// Indicates whether the batch was partially successful.
    /// </summary>
    public bool IsPartialSuccess => SuccessCount > 0 && FailureCount > 0;

    /// <summary>
    /// Storage tiers where entries were written.
    /// </summary>
    public Dictionary<StorageTier, int> TierDistribution { get; set; } = new();

    public static BatchOperationResult Success(Guid batchId, int count, long processingTimeMs)
    {
        return new BatchOperationResult
        {
            BatchId = batchId,
            SuccessCount = count,
            TotalCount = count,
            ProcessingTimeMs = processingTimeMs
        };
    }

    public static BatchOperationResult Failure(Guid batchId, int count, string error)
    {
        return new BatchOperationResult
        {
            BatchId = batchId,
            FailureCount = count,
            TotalCount = count,
            Errors = new List<string> { error }
        };
    }

    public override string ToString()
    {
        return $"BatchOperationResult[{BatchId}]: {SuccessCount}/{TotalCount} successful in {ProcessingTimeMs}ms";
    }
}

/// <summary>
/// Represents storage statistics across all tiers.
/// </summary>
public class HistoryStorageStats
{
    /// <summary>
    /// Statistics for each storage tier.
    /// </summary>
    public Dictionary<StorageTier, TierStorageStats> TierStats { get; set; } = new();

    /// <summary>
    /// Total number of history entries across all tiers.
    /// </summary>
    public long TotalEntries => TierStats.Values.Sum(s => s.EntryCount);

    /// <summary>
    /// Total storage size across all tiers in bytes.
    /// </summary>
    public long TotalSizeBytes => TierStats.Values.Sum(s => s.SizeBytes);

    /// <summary>
    /// Estimated monthly storage cost across all tiers.
    /// </summary>
    public decimal EstimatedMonthlyCost => TierStats.Values.Sum(s => s.EstimatedMonthlyCost);

    /// <summary>
    /// When these statistics were collected.
    /// </summary>
    public DateTime CollectedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets a summary of the storage distribution.
    /// </summary>
    public string GetDistributionSummary()
    {
        var total = TotalEntries;
        if (total == 0) return "No data";

        var parts = TierStats.Select(kvp => 
            $"{kvp.Key}: {kvp.Value.EntryCount:N0} ({kvp.Value.EntryCount * 100.0 / total:F1}%)");
        
        return string.Join(", ", parts);
    }

    public override string ToString()
    {
        return $"HistoryStorageStats: {TotalEntries:N0} entries, {TotalSizeBytes / (1024 * 1024):N0} MB, ${EstimatedMonthlyCost:F2}/month";
    }
}

/// <summary>
/// Represents storage statistics for a specific tier.
/// </summary>
public class TierStorageStats
{
    /// <summary>
    /// The storage tier these statistics represent.
    /// </summary>
    public StorageTier Tier { get; set; }

    /// <summary>
    /// Number of history entries in this tier.
    /// </summary>
    public long EntryCount { get; set; }

    /// <summary>
    /// Total storage size in bytes.
    /// </summary>
    public long SizeBytes { get; set; }

    /// <summary>
    /// Average query response time in milliseconds.
    /// </summary>
    public double AverageQueryTimeMs { get; set; }

    /// <summary>
    /// Estimated monthly storage cost for this tier.
    /// </summary>
    public decimal EstimatedMonthlyCost { get; set; }

    /// <summary>
    /// Date range of data in this tier.
    /// </summary>
    public DateTime? OldestEntry { get; set; }

    /// <summary>
    /// Date range of data in this tier.
    /// </summary>
    public DateTime? NewestEntry { get; set; }

    /// <summary>
    /// Number of queries served from this tier in the last 24 hours.
    /// </summary>
    public long QueriesLast24Hours { get; set; }

    /// <summary>
    /// Cache hit rate for this tier (0.0 to 1.0).
    /// </summary>
    public double CacheHitRate { get; set; }

    /// <summary>
    /// Gets the size in a human-readable format.
    /// </summary>
    public string GetFormattedSize()
    {
        const long KB = 1024;
        const long MB = KB * 1024;
        const long GB = MB * 1024;

        return SizeBytes switch
        {
            < KB => $"{SizeBytes} bytes",
            < MB => $"{SizeBytes / (double)KB:F1} KB",
            < GB => $"{SizeBytes / (double)MB:F1} MB",
            _ => $"{SizeBytes / (double)GB:F1} GB"
        };
    }

    public override string ToString()
    {
        return $"TierStorageStats[{Tier}]: {EntryCount:N0} entries, {GetFormattedSize()}, {AverageQueryTimeMs:F1}ms avg query";
    }
}

/// <summary>
/// Represents the result of data integrity validation.
/// </summary>
public class DataIntegrityResult
{
    /// <summary>
    /// Overall integrity status.
    /// </summary>
    public bool IsValid { get; set; } = true;

    /// <summary>
    /// List of integrity issues found.
    /// </summary>
    public List<IntegrityIssue> Issues { get; set; } = new();

    /// <summary>
    /// Statistics about the validation process.
    /// </summary>
    public Dictionary<StorageTier, long> RecordsValidated { get; set; } = new();

    /// <summary>
    /// Time taken to perform validation in milliseconds.
    /// </summary>
    public long ValidationTimeMs { get; set; }

    /// <summary>
    /// When the validation was performed.
    /// </summary>
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Adds an integrity issue to the result.
    /// </summary>
    public void AddIssue(IntegrityIssueType type, string description, StorageTier? tier = null)
    {
        Issues.Add(new IntegrityIssue
        {
            Type = type,
            Description = description,
            Tier = tier,
            DetectedAt = DateTime.UtcNow
        });

        if (type == IntegrityIssueType.Critical || type == IntegrityIssueType.Error)
        {
            IsValid = false;
        }
    }

    /// <summary>
    /// Gets a summary of issues by type.
    /// </summary>
    public string GetIssueSummary()
    {
        if (!Issues.Any()) return "No issues found";

        var groups = Issues.GroupBy(i => i.Type);
        var parts = groups.Select(g => $"{g.Count()} {g.Key.ToString().ToLower()}(s)");
        
        return string.Join(", ", parts);
    }

    public override string ToString()
    {
        var status = IsValid ? "VALID" : "INVALID";
        var totalRecords = RecordsValidated.Values.Sum();
        return $"DataIntegrityResult: {status}, {totalRecords:N0} records validated, {GetIssueSummary()}";
    }
}

/// <summary>
/// Represents a data integrity issue.
/// </summary>
public class IntegrityIssue
{
    /// <summary>
    /// Type of integrity issue.
    /// </summary>
    public IntegrityIssueType Type { get; set; }

    /// <summary>
    /// Description of the issue.
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Storage tier where the issue was found.
    /// </summary>
    public StorageTier? Tier { get; set; }

    /// <summary>
    /// When the issue was detected.
    /// </summary>
    public DateTime DetectedAt { get; set; }

    /// <summary>
    /// Additional context about the issue.
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();

    public override string ToString()
    {
        var tierInfo = Tier.HasValue ? $" in {Tier} tier" : "";
        return $"{Type}: {Description}{tierInfo}";
    }
}

/// <summary>
/// Types of data integrity issues.
/// </summary>
public enum IntegrityIssueType
{
    /// <summary>
    /// Critical issue that requires immediate attention.
    /// </summary>
    Critical = 1,

    /// <summary>
    /// Error that affects data quality.
    /// </summary>
    Error = 2,

    /// <summary>
    /// Warning about potential issues.
    /// </summary>
    Warning = 3,

    /// <summary>
    /// Informational message.
    /// </summary>
    Info = 4
}
