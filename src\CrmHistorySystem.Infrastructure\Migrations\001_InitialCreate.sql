-- =============================================
-- CRM History System - Initial Database Schema
-- Version: 1.0.0
-- Description: Creates optimized tables for hot and warm tier storage
-- =============================================

-- Create Hot Tier Table
-- Optimized for maximum performance with SSD storage
CREATE TABLE [dbo].[HistoryEntries_Hot] (
    [Id] BIGINT IDENTITY(1,1) NOT NULL,
    [LeadId] INT NOT NULL,
    [FieldName] NVARCHAR(100) NOT NULL,
    [OldValue] NVARCHAR(4000) NULL,
    [NewValue] NVARCHAR(4000) NULL,
    [ChangedAt] DATETIME2(3) NOT NULL CONSTRAINT [DF_HistoryEntries_Hot_ChangedAt] DEFAULT (GETUTCDATE()),
    [ChangedBy] NVARCHAR(100) NOT NULL,
    [Metadata] NVARCHAR(2000) NULL,
    
    -- Primary key (non-clustered for hot tier)
    CONSTRAINT [PK_HistoryEntries_Hot] PRIMARY KEY NONCLUSTERED ([Id]),
    
    -- Check constraints for data integrity
    CONSTRAINT [CK_HistoryEntries_Hot_LeadId] CHECK ([LeadId] > 0),
    CONSTRAINT [CK_HistoryEntries_Hot_FieldName] CHECK (LEN([FieldName]) > 0),
    CONSTRAINT [CK_HistoryEntries_Hot_ChangedBy] CHECK (LEN([ChangedBy]) > 0),
    CONSTRAINT [CK_HistoryEntries_Hot_Values] CHECK ([OldValue] IS NOT NULL OR [NewValue] IS NOT NULL)
);

-- Clustered index for optimal range queries (LeadId + ChangedAt)
CREATE CLUSTERED INDEX [IX_HistoryEntries_Hot_LeadId_ChangedAt_Clustered] 
ON [dbo].[HistoryEntries_Hot] ([LeadId] ASC, [ChangedAt] DESC);

-- Non-clustered indexes for common query patterns
CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Hot_ChangedAt] 
ON [dbo].[HistoryEntries_Hot] ([ChangedAt] DESC)
WHERE [ChangedAt] >= DATEADD(day, -90, GETUTCDATE());

CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Hot_FieldName_ChangedAt] 
ON [dbo].[HistoryEntries_Hot] ([FieldName] ASC, [ChangedAt] DESC)
WHERE [ChangedAt] >= DATEADD(day, -90, GETUTCDATE());

CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Hot_ChangedBy_ChangedAt] 
ON [dbo].[HistoryEntries_Hot] ([ChangedBy] ASC, [ChangedAt] DESC)
WHERE [ChangedAt] >= DATEADD(day, -90, GETUTCDATE());

-- Covering index for common SELECT scenarios
CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Hot_Covering] 
ON [dbo].[HistoryEntries_Hot] ([LeadId] ASC, [FieldName] ASC, [ChangedAt] DESC)
INCLUDE ([OldValue], [NewValue], [ChangedBy])
WHERE [ChangedAt] >= DATEADD(day, -90, GETUTCDATE());

-- =============================================

-- Create Warm Tier Table
-- Optimized for balanced performance and storage efficiency
CREATE TABLE [dbo].[HistoryEntries_Warm] (
    [Id] BIGINT IDENTITY(1,1) NOT NULL,
    [LeadId] INT NOT NULL,
    [FieldName] NVARCHAR(100) NOT NULL,
    [OldValue] NVARCHAR(4000) NULL,
    [NewValue] NVARCHAR(4000) NULL,
    [ChangedAt] DATETIME2(3) NOT NULL CONSTRAINT [DF_HistoryEntries_Warm_ChangedAt] DEFAULT (GETUTCDATE()),
    [ChangedBy] NVARCHAR(100) NOT NULL,
    [Metadata] NVARCHAR(2000) NULL,
    
    -- Primary key (non-clustered for warm tier)
    CONSTRAINT [PK_HistoryEntries_Warm] PRIMARY KEY NONCLUSTERED ([Id]),
    
    -- Check constraints for data integrity
    CONSTRAINT [CK_HistoryEntries_Warm_LeadId] CHECK ([LeadId] > 0),
    CONSTRAINT [CK_HistoryEntries_Warm_FieldName] CHECK (LEN([FieldName]) > 0),
    CONSTRAINT [CK_HistoryEntries_Warm_ChangedBy] CHECK (LEN([ChangedBy]) > 0),
    CONSTRAINT [CK_HistoryEntries_Warm_Values] CHECK ([OldValue] IS NOT NULL OR [NewValue] IS NOT NULL)
) WITH (DATA_COMPRESSION = PAGE); -- Enable page compression for storage efficiency

-- Clustered index optimized for date-based partitioning
CREATE CLUSTERED INDEX [IX_HistoryEntries_Warm_ChangedAt_LeadId_Clustered] 
ON [dbo].[HistoryEntries_Warm] ([ChangedAt] DESC, [LeadId] ASC);

-- Essential non-clustered indexes
CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Warm_LeadId_ChangedAt] 
ON [dbo].[HistoryEntries_Warm] ([LeadId] ASC, [ChangedAt] DESC);

CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Warm_FieldName] 
ON [dbo].[HistoryEntries_Warm] ([FieldName] ASC);

-- =============================================

-- Create partition function for date-based partitioning (Warm tier)
-- This will be used for monthly partitions
CREATE PARTITION FUNCTION [PF_HistoryByMonth] (DATETIME2(3))
AS RANGE RIGHT FOR VALUES (
    '2023-01-01', '2023-02-01', '2023-03-01', '2023-04-01', '2023-05-01', '2023-06-01',
    '2023-07-01', '2023-08-01', '2023-09-01', '2023-10-01', '2023-11-01', '2023-12-01',
    '2024-01-01', '2024-02-01', '2024-03-01', '2024-04-01', '2024-05-01', '2024-06-01',
    '2024-07-01', '2024-08-01', '2024-09-01', '2024-10-01', '2024-11-01', '2024-12-01',
    '2025-01-01', '2025-02-01', '2025-03-01', '2025-04-01', '2025-05-01', '2025-06-01',
    '2025-07-01', '2025-08-01', '2025-09-01', '2025-10-01', '2025-11-01', '2025-12-01'
);

-- Create partition scheme
CREATE PARTITION SCHEME [PS_HistoryByMonth]
AS PARTITION [PF_HistoryByMonth]
ALL TO ([PRIMARY]);

-- =============================================

-- Create stored procedures for common operations

-- Bulk insert procedure for hot tier
CREATE PROCEDURE [dbo].[sp_BulkInsertHistoryHot]
    @Entries NVARCHAR(MAX) -- JSON array of history entries
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        INSERT INTO [dbo].[HistoryEntries_Hot] 
        ([LeadId], [FieldName], [OldValue], [NewValue], [ChangedAt], [ChangedBy], [Metadata])
        SELECT 
            [LeadId],
            [FieldName],
            [OldValue],
            [NewValue],
            ISNULL(TRY_CAST([ChangedAt] AS DATETIME2(3)), GETUTCDATE()),
            [ChangedBy],
            [Metadata]
        FROM OPENJSON(@Entries)
        WITH (
            [LeadId] INT '$.leadId',
            [FieldName] NVARCHAR(100) '$.fieldName',
            [OldValue] NVARCHAR(4000) '$.oldValue',
            [NewValue] NVARCHAR(4000) '$.newValue',
            [ChangedAt] NVARCHAR(50) '$.changedAt',
            [ChangedBy] NVARCHAR(100) '$.changedBy',
            [Metadata] NVARCHAR(2000) '$.metadata'
        )
        WHERE [LeadId] IS NOT NULL 
          AND [FieldName] IS NOT NULL 
          AND [ChangedBy] IS NOT NULL;
        
        COMMIT TRANSACTION;
        
        SELECT @@ROWCOUNT AS [RowsInserted];
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        THROW;
    END CATCH
END;

-- Bulk insert procedure for warm tier
CREATE PROCEDURE [dbo].[sp_BulkInsertHistoryWarm]
    @Entries NVARCHAR(MAX) -- JSON array of history entries
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        INSERT INTO [dbo].[HistoryEntries_Warm] 
        ([LeadId], [FieldName], [OldValue], [NewValue], [ChangedAt], [ChangedBy], [Metadata])
        SELECT 
            [LeadId],
            [FieldName],
            [OldValue],
            [NewValue],
            ISNULL(TRY_CAST([ChangedAt] AS DATETIME2(3)), GETUTCDATE()),
            [ChangedBy],
            [Metadata]
        FROM OPENJSON(@Entries)
        WITH (
            [LeadId] INT '$.leadId',
            [FieldName] NVARCHAR(100) '$.fieldName',
            [OldValue] NVARCHAR(4000) '$.oldValue',
            [NewValue] NVARCHAR(4000) '$.newValue',
            [ChangedAt] NVARCHAR(50) '$.changedAt',
            [ChangedBy] NVARCHAR(100) '$.changedBy',
            [Metadata] NVARCHAR(2000) '$.metadata'
        )
        WHERE [LeadId] IS NOT NULL 
          AND [FieldName] IS NOT NULL 
          AND [ChangedBy] IS NOT NULL;
        
        COMMIT TRANSACTION;
        
        SELECT @@ROWCOUNT AS [RowsInserted];
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        THROW;
    END CATCH
END;

-- =============================================

-- Create views for unified access across tiers
CREATE VIEW [dbo].[vw_HistoryEntries_All]
AS
SELECT 
    [Id],
    [LeadId],
    [FieldName],
    [OldValue],
    [NewValue],
    [ChangedAt],
    [ChangedBy],
    [Metadata],
    'Hot' AS [StorageTier]
FROM [dbo].[HistoryEntries_Hot]

UNION ALL

SELECT 
    [Id],
    [LeadId],
    [FieldName],
    [OldValue],
    [NewValue],
    [ChangedAt],
    [ChangedBy],
    [Metadata],
    'Warm' AS [StorageTier]
FROM [dbo].[HistoryEntries_Warm];

-- =============================================

-- Create statistics update job
CREATE PROCEDURE [dbo].[sp_UpdateHistoryStatistics]
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Update statistics for hot tier
    UPDATE STATISTICS [dbo].[HistoryEntries_Hot];
    
    -- Update statistics for warm tier
    UPDATE STATISTICS [dbo].[HistoryEntries_Warm];
    
    PRINT 'Statistics updated successfully';
END;

-- =============================================

PRINT 'CRM History System database schema created successfully';
PRINT 'Hot tier table: HistoryEntries_Hot (optimized for performance)';
PRINT 'Warm tier table: HistoryEntries_Warm (optimized for storage efficiency)';
PRINT 'Partition scheme: PS_HistoryByMonth (for warm tier partitioning)';
PRINT 'Views: vw_HistoryEntries_All (unified access across tiers)';
PRINT 'Stored procedures: sp_BulkInsertHistoryHot, sp_BulkInsertHistoryWarm, sp_UpdateHistoryStatistics';
