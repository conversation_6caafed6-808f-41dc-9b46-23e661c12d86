using FluentValidation;
using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Core.Validation;

/// <summary>
/// Validator for HistoryEntry objects.
/// </summary>
public class HistoryEntryValidator : AbstractValidator<HistoryEntry>
{
    public HistoryEntryValidator()
    {
        RuleFor(x => x.LeadId)
            .GreaterThan(0)
            .WithMessage("LeadId must be greater than 0");

        RuleFor(x => x.FieldName)
            .NotEmpty()
            .WithMessage("FieldName is required")
            .MaximumLength(100)
            .WithMessage("FieldName cannot exceed 100 characters")
            .Matches("^[a-zA-Z][a-zA-Z0-9_]*$")
            .WithMessage("FieldName must start with a letter and contain only letters, numbers, and underscores");

        RuleFor(x => x.ChangedBy)
            .NotEmpty()
            .WithMessage("ChangedBy is required")
            .MaximumLength(100)
            .WithMessage("ChangedBy cannot exceed 100 characters");

        RuleFor(x => x.ChangedAt)
            .NotEqual(default(DateTime))
            .WithMessage("ChangedAt is required")
            .LessThanOrEqualTo(DateTime.UtcNow.AddMinutes(5))
            .WithMessage("ChangedAt cannot be in the future (allowing 5 minute clock skew)");

        RuleFor(x => x.OldValue)
            .MaximumLength(4000)
            .WithMessage("OldValue cannot exceed 4000 characters");

        RuleFor(x => x.NewValue)
            .MaximumLength(4000)
            .WithMessage("NewValue cannot exceed 4000 characters");

        RuleFor(x => x.Metadata)
            .MaximumLength(2000)
            .WithMessage("Metadata cannot exceed 2000 characters")
            .Must(BeValidJson)
            .When(x => !string.IsNullOrEmpty(x.Metadata))
            .WithMessage("Metadata must be valid JSON");

        // Business rule: OldValue and NewValue cannot both be null/empty
        RuleFor(x => x)
            .Must(x => !string.IsNullOrEmpty(x.OldValue) || !string.IsNullOrEmpty(x.NewValue))
            .WithMessage("Either OldValue or NewValue must have a value");
    }

    private static bool BeValidJson(string? json)
    {
        if (string.IsNullOrWhiteSpace(json))
            return true;

        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }
}

/// <summary>
/// Validator for HistoryQuery objects.
/// </summary>
public class HistoryQueryValidator : AbstractValidator<HistoryQuery>
{
    public HistoryQueryValidator()
    {
        RuleFor(x => x.LeadId)
            .GreaterThan(0)
            .When(x => x.LeadId.HasValue)
            .WithMessage("LeadId must be greater than 0 when specified");

        RuleFor(x => x.FieldName)
            .MaximumLength(100)
            .WithMessage("FieldName cannot exceed 100 characters")
            .Matches("^[a-zA-Z][a-zA-Z0-9_]*$")
            .When(x => !string.IsNullOrEmpty(x.FieldName))
            .WithMessage("FieldName must start with a letter and contain only letters, numbers, and underscores");

        RuleFor(x => x.ChangedBy)
            .MaximumLength(100)
            .WithMessage("ChangedBy cannot exceed 100 characters");

        RuleFor(x => x.Page)
            .GreaterThan(0)
            .WithMessage("Page must be greater than 0");

        RuleFor(x => x.PageSize)
            .InclusiveBetween(1, 1000)
            .WithMessage("PageSize must be between 1 and 1000");

        RuleFor(x => x.StartDate)
            .LessThanOrEqualTo(DateTime.UtcNow)
            .When(x => x.StartDate.HasValue)
            .WithMessage("StartDate cannot be in the future");

        RuleFor(x => x.EndDate)
            .LessThanOrEqualTo(DateTime.UtcNow)
            .When(x => x.EndDate.HasValue)
            .WithMessage("EndDate cannot be in the future");

        RuleFor(x => x)
            .Must(x => !x.StartDate.HasValue || !x.EndDate.HasValue || x.StartDate <= x.EndDate)
            .WithMessage("StartDate must be less than or equal to EndDate");

        // Business rule: Date range cannot exceed 2 years for performance reasons
        RuleFor(x => x)
            .Must(x => !x.StartDate.HasValue || !x.EndDate.HasValue || 
                      (x.EndDate.Value - x.StartDate.Value).TotalDays <= 730)
            .WithMessage("Date range cannot exceed 2 years");
    }
}

/// <summary>
/// Validator for HistoryBatch objects.
/// </summary>
public class HistoryBatchValidator : AbstractValidator<HistoryBatch>
{
    public HistoryBatchValidator()
    {
        RuleFor(x => x.BatchId)
            .NotEqual(Guid.Empty)
            .WithMessage("BatchId cannot be empty");

        RuleFor(x => x.CreatedBy)
            .NotEmpty()
            .WithMessage("CreatedBy is required")
            .MaximumLength(100)
            .WithMessage("CreatedBy cannot exceed 100 characters");

        RuleFor(x => x.Entries)
            .NotNull()
            .WithMessage("Entries collection cannot be null")
            .NotEmpty()
            .WithMessage("Batch cannot be empty")
            .Must(x => x.Count <= HistoryBatch.MaxBatchSize)
            .WithMessage($"Batch cannot contain more than {HistoryBatch.MaxBatchSize} entries");

        RuleFor(x => x.CreatedAt)
            .NotEqual(default(DateTime))
            .WithMessage("CreatedAt is required")
            .LessThanOrEqualTo(DateTime.UtcNow.AddMinutes(5))
            .WithMessage("CreatedAt cannot be in the future (allowing 5 minute clock skew)");

        RuleFor(x => x.Metadata)
            .MaximumLength(2000)
            .WithMessage("Metadata cannot exceed 2000 characters");

        // Validate each entry in the batch
        RuleForEach(x => x.Entries)
            .SetValidator(new HistoryEntryValidator());

        // Business rule: Check for duplicate entries within the batch
        RuleFor(x => x.Entries)
            .Must(NotContainDuplicates)
            .WithMessage("Batch cannot contain duplicate entries");
    }

    private static bool NotContainDuplicates(IList<HistoryEntry> entries)
    {
        if (entries == null || entries.Count <= 1)
            return true;

        var uniqueKeys = entries
            .Select(e => new { e.LeadId, e.FieldName, e.ChangedAt, e.ChangedBy })
            .Distinct()
            .Count();

        return uniqueKeys == entries.Count;
    }
}

/// <summary>
/// Validator for configuration options.
/// </summary>
public class HistoryOptionsValidator : AbstractValidator<Configuration.HistoryOptions>
{
    public HistoryOptionsValidator()
    {
        RuleFor(x => x.HotTierRetentionDays)
            .InclusiveBetween(1, 365)
            .WithMessage("HotTierRetentionDays must be between 1 and 365");

        RuleFor(x => x.WarmTierRetentionDays)
            .InclusiveBetween(1, 3650)
            .WithMessage("WarmTierRetentionDays must be between 1 and 3650");

        RuleFor(x => x)
            .Must(x => x.HotTierRetentionDays < x.WarmTierRetentionDays)
            .WithMessage("HotTierRetentionDays must be less than WarmTierRetentionDays");

        RuleFor(x => x.BatchSize)
            .InclusiveBetween(1, 10000)
            .WithMessage("BatchSize must be between 1 and 10000");

        RuleFor(x => x.CacheExpirationMinutes)
            .InclusiveBetween(1, 1440)
            .WithMessage("CacheExpirationMinutes must be between 1 and 1440");

        RuleFor(x => x.MaxConcurrentConnections)
            .InclusiveBetween(1, 100)
            .WithMessage("MaxConcurrentConnections must be between 1 and 100");

        RuleFor(x => x.QueryTimeoutSeconds)
            .InclusiveBetween(1, 300)
            .WithMessage("QueryTimeoutSeconds must be between 1 and 300");

        RuleFor(x => x.MaxRetryAttempts)
            .InclusiveBetween(0, 10)
            .WithMessage("MaxRetryAttempts must be between 0 and 10");

        RuleFor(x => x.InitialRetryDelayMs)
            .InclusiveBetween(100, 10000)
            .WithMessage("InitialRetryDelayMs must be between 100 and 10000");

        RuleFor(x => x.MaxRetryDelayMs)
            .InclusiveBetween(1000, 60000)
            .WithMessage("MaxRetryDelayMs must be between 1000 and 60000");

        RuleFor(x => x)
            .Must(x => x.InitialRetryDelayMs < x.MaxRetryDelayMs)
            .WithMessage("InitialRetryDelayMs must be less than MaxRetryDelayMs");

        RuleFor(x => x.ConnectionStrings.Hot)
            .NotEmpty()
            .WithMessage("Hot tier connection string is required");

        RuleFor(x => x.ConnectionStrings.Warm)
            .NotEmpty()
            .WithMessage("Warm tier connection string is required");

        RuleFor(x => x.ConnectionStrings.Cold)
            .NotEmpty()
            .WithMessage("Cold tier connection string is required");
    }
}
