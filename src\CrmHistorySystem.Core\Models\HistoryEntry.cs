using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Core.Models;

/// <summary>
/// Represents a single field change history entry in the CRM system.
/// Optimized for high-performance storage and retrieval across tiered storage.
/// </summary>
public class HistoryEntry
{
    /// <summary>
    /// Unique identifier for the history entry.
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// The ID of the lead that was modified.
    /// </summary>
    [Required]
    public int LeadId { get; set; }

    /// <summary>
    /// The name of the field that was changed.
    /// Maximum length of 100 characters for optimal indexing.
    /// </summary>
    [Required]
    [StringLength(100)]
    public string FieldName { get; set; } = string.Empty;

    /// <summary>
    /// The previous value of the field before the change.
    /// Nullable to support new field creation scenarios.
    /// </summary>
    public string? OldValue { get; set; }

    /// <summary>
    /// The new value of the field after the change.
    /// </summary>
    public string? NewValue { get; set; }

    /// <summary>
    /// Timestamp when the change occurred.
    /// Used for tiered storage routing and query optimization.
    /// </summary>
    [Required]
    public DateTime ChangedAt { get; set; }

    /// <summary>
    /// Identifier of the user who made the change.
    /// Maximum length of 100 characters for optimal storage.
    /// </summary>
    [Required]
    [StringLength(100)]
    public string ChangedBy { get; set; } = string.Empty;

    /// <summary>
    /// Optional metadata for additional context about the change.
    /// Stored as JSON for flexibility while maintaining performance.
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Determines which storage tier this entry belongs to based on age.
    /// </summary>
    public StorageTier GetStorageTier()
    {
        var age = DateTime.UtcNow - ChangedAt;
        
        return age.TotalDays switch
        {
            <= 90 => StorageTier.Hot,
            <= 365 => StorageTier.Warm,
            _ => StorageTier.Cold
        };
    }

    /// <summary>
    /// Creates a copy of the history entry for archival purposes.
    /// </summary>
    public HistoryEntry Clone()
    {
        return new HistoryEntry
        {
            Id = Id,
            LeadId = LeadId,
            FieldName = FieldName,
            OldValue = OldValue,
            NewValue = NewValue,
            ChangedAt = ChangedAt,
            ChangedBy = ChangedBy,
            Metadata = Metadata
        };
    }

    /// <summary>
    /// Validates that the history entry contains required data.
    /// </summary>
    public bool IsValid()
    {
        return LeadId > 0 
            && !string.IsNullOrWhiteSpace(FieldName) 
            && !string.IsNullOrWhiteSpace(ChangedBy)
            && ChangedAt != default;
    }

    public override string ToString()
    {
        return $"HistoryEntry[{Id}]: Lead {LeadId}, Field '{FieldName}', Changed by {ChangedBy} at {ChangedAt:yyyy-MM-dd HH:mm:ss}";
    }
}

/// <summary>
/// Defines the storage tiers for the tiered storage architecture.
/// </summary>
public enum StorageTier
{
    /// <summary>
    /// Hot tier: Last 3 months, optimized for speed (SQL Server with SSD).
    /// </summary>
    Hot = 1,

    /// <summary>
    /// Warm tier: 3-12 months, balanced performance and cost (SQL Server with compression).
    /// </summary>
    Warm = 2,

    /// <summary>
    /// Cold tier: 12+ months, optimized for cost (Azure Blob Storage).
    /// </summary>
    Cold = 3
}
