using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Core.Models;

/// <summary>
/// Represents a lead entity in the CRM system.
/// This model matches the existing Lead entity structure for integration purposes.
/// </summary>
public class Lead
{
    private string? name;
    private string? alternateContactNo;
    private string? leadNumber;

    /// <summary>
    /// Unique identifier for the lead.
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Name of the lead. Returns "Unknown" if null or empty.
    /// </summary>
    public string Name 
    { 
        get => string.IsNullOrEmpty(name) ? "Unknown" : name; 
        set => name = value; 
    }

    /// <summary>
    /// Primary contact number for the lead.
    /// </summary>
    [Required]
    public string ContactNo { get; set; } = default!;

    /// <summary>
    /// Alternate contact number with validation.
    /// </summary>
    public string? AlternateContactNo 
    { 
        get => alternateContactNo; 
        set => alternateContactNo = value?.ValidateContactNumber(); 
    }

    /// <summary>
    /// Landline phone number.
    /// </summary>
    public string? LandLine { get; set; }

    /// <summary>
    /// Email address of the lead.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// General notes about the lead.
    /// </summary>
    public string? Notes { get; set; }

    /// <summary>
    /// Confidential notes about the lead.
    /// </summary>
    public string? ConfidentialNotes { get; set; }

    /// <summary>
    /// Scheduled date for follow-up or meeting.
    /// </summary>
    public DateTime? ScheduledDate { get; set; }

    /// <summary>
    /// Date to revert or follow up on the lead.
    /// </summary>
    public DateTime? RevertDate { get; set; }

    /// <summary>
    /// Project chosen by the lead.
    /// </summary>
    public string? ChosenProject { get; set; }

    /// <summary>
    /// Property chosen by the lead.
    /// </summary>
    public string? ChosenProperty { get; set; }

    /// <summary>
    /// Name under which the booking is made.
    /// </summary>
    public string? BookedUnderName { get; set; }

    /// <summary>
    /// Lead number with keyboard character extraction.
    /// </summary>
    public string? LeadNumber 
    { 
        get => leadNumber; 
        set => leadNumber = value?.ExtractKeyboardCharacters(); 
    }

    /// <summary>
    /// User ID to whom the lead is assigned.
    /// </summary>
    public Guid AssignTo { get; set; }

    /// <summary>
    /// Number of times the lead has been shared.
    /// </summary>
    public int ShareCount { get; set; }

    /// <summary>
    /// Price at which the lead was sold.
    /// </summary>
    public string? SoldPrice { get; set; }

    /// <summary>
    /// Timestamp when the lead was created.
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Timestamp when the lead was last updated.
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// User who created the lead.
    /// </summary>
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// User who last updated the lead.
    /// </summary>
    public string UpdatedBy { get; set; } = string.Empty;
}

/// <summary>
/// Extension methods for lead-related operations.
/// These methods provide the same functionality as in your existing system.
/// </summary>
public static class LeadExtensions
{
    /// <summary>
    /// Validates and formats a contact number.
    /// </summary>
    public static string? ValidateContactNumber(this string? contactNumber)
    {
        if (string.IsNullOrWhiteSpace(contactNumber))
            return null;

        // Remove all non-digit characters
        var digitsOnly = new string(contactNumber.Where(char.IsDigit).ToArray());
        
        // Basic validation - adjust according to your business rules
        if (digitsOnly.Length < 10 || digitsOnly.Length > 15)
            return contactNumber; // Return original if validation fails
        
        return digitsOnly;
    }

    /// <summary>
    /// Extracts keyboard characters from a string.
    /// </summary>
    public static string? ExtractKeyboardCharacters(this string? input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return null;

        // Extract only printable ASCII characters (keyboard characters)
        var keyboardChars = input.Where(c => c >= 32 && c <= 126).ToArray();
        
        return keyboardChars.Length > 0 ? new string(keyboardChars) : null;
    }

    /// <summary>
    /// Creates a deep copy of the lead for comparison purposes.
    /// </summary>
    public static Lead Clone(this Lead lead)
    {
        return new Lead
        {
            Id = lead.Id,
            Name = lead.Name,
            ContactNo = lead.ContactNo,
            AlternateContactNo = lead.AlternateContactNo,
            LandLine = lead.LandLine,
            Email = lead.Email,
            Notes = lead.Notes,
            ConfidentialNotes = lead.ConfidentialNotes,
            ScheduledDate = lead.ScheduledDate,
            RevertDate = lead.RevertDate,
            ChosenProject = lead.ChosenProject,
            ChosenProperty = lead.ChosenProperty,
            BookedUnderName = lead.BookedUnderName,
            LeadNumber = lead.LeadNumber,
            AssignTo = lead.AssignTo,
            ShareCount = lead.ShareCount,
            SoldPrice = lead.SoldPrice,
            CreatedAt = lead.CreatedAt,
            UpdatedAt = lead.UpdatedAt,
            CreatedBy = lead.CreatedBy,
            UpdatedBy = lead.UpdatedBy
        };
    }
}
