using Xunit;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Core.Configuration;
using CrmHistorySystem.Infrastructure.Data;
using CrmHistorySystem.Infrastructure.Services;
using CrmHistorySystem.Infrastructure.Storage;
using CrmHistorySystem.Infrastructure.Caching;

namespace CrmHistorySystem.Tests.Integration;

public class TieredHistoryServiceIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly IHistoryService _historyService;

    public TieredHistoryServiceIntegrationTests()
    {
        var services = new ServiceCollection();
        
        // Configure test services
        services.AddLogging(builder => builder.AddConsole());
        
        // Configure test database
        services.AddDbContext<HistoryDbContext>(options =>
            options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}"));

        // Configure test options
        var historyOptions = new HistoryOptions
        {
            HotTierRetentionDays = 90,
            WarmTierRetentionDays = 365,
            BatchSize = 1000,
            CacheExpirationMinutes = 60,
            ConnectionStrings = new ConnectionStrings
            {
                Hot = "InMemory",
                Warm = "InMemory",
                Cold = "InMemory",
                Redis = "localhost:6379"
            }
        };
        services.Configure<HistoryOptions>(opt =>
        {
            opt.HotTierRetentionDays = historyOptions.HotTierRetentionDays;
            opt.WarmTierRetentionDays = historyOptions.WarmTierRetentionDays;
            opt.BatchSize = historyOptions.BatchSize;
            opt.CacheExpirationMinutes = historyOptions.CacheExpirationMinutes;
            opt.ConnectionStrings = historyOptions.ConnectionStrings;
        });

        // Add cache (in-memory for testing)
        services.AddMemoryCache();
        services.AddSingleton<Microsoft.Extensions.Caching.Distributed.IDistributedCache, 
            Microsoft.Extensions.Caching.Memory.MemoryDistributedCache>();
        services.AddSingleton<IHistoryCache, RedisHistoryCache>();

        // Add tier storages (mock implementations for testing)
        services.AddScoped<ITierStorage>(provider => 
            new MockTierStorage(StorageTier.Hot, provider.GetRequiredService<ILogger<MockTierStorage>>()));
        services.AddScoped<ITierStorage>(provider => 
            new MockTierStorage(StorageTier.Warm, provider.GetRequiredService<ILogger<MockTierStorage>>()));
        services.AddScoped<ITierStorage>(provider => 
            new MockTierStorage(StorageTier.Cold, provider.GetRequiredService<ILogger<MockTierStorage>>()));

        // Add main service
        services.AddScoped<IHistoryService, TieredHistoryService>();

        _serviceProvider = services.BuildServiceProvider();
        _historyService = _serviceProvider.GetRequiredService<IHistoryService>();
    }

    [Fact]
    public async Task GetHistoryAsync_WithValidQuery_ReturnsResults()
    {
        // Arrange
        var testEntries = CreateTestEntries(100);
        await _historyService.AddHistoryBatchAsync(testEntries);

        var query = new HistoryQuery
        {
            LeadId = 1,
            Page = 1,
            PageSize = 50
        };

        // Act
        var result = await _historyService.GetHistoryAsync(query);

        // Assert
        result.Should().NotBeNull();
        result.Data.Should().NotBeEmpty();
        result.CurrentPage.Should().Be(1);
        result.PageSize.Should().Be(50);
        result.ExecutionTimeMs.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GetHistoryAsync_WithDateRangeSpanningTiers_QueriesMultipleTiers()
    {
        // Arrange
        var testEntries = CreateTestEntriesAcrossTiers();
        await _historyService.AddHistoryBatchAsync(testEntries);

        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-200),
            EndDate = DateTime.UtcNow.AddDays(-30),
            Page = 1,
            PageSize = 100
        };

        // Act
        var result = await _historyService.GetHistoryAsync(query);

        // Assert
        result.Should().NotBeNull();
        result.QueriedTiers.Should().HaveCountGreaterThan(1);
        result.QueriedTiers.Should().Contain(StorageTier.Hot);
        result.QueriedTiers.Should().Contain(StorageTier.Warm);
    }

    [Fact]
    public async Task AddHistoryEntryAsync_WithValidEntry_ReturnsTrue()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            LeadId = 1,
            FieldName = "TestField",
            OldValue = "OldValue",
            NewValue = "NewValue",
            ChangedAt = DateTime.UtcNow,
            ChangedBy = "TestUser"
        };

        // Act
        var result = await _historyService.AddHistoryEntryAsync(entry);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task AddHistoryEntryAsync_WithInvalidEntry_ReturnsFalse()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            LeadId = 0, // Invalid
            FieldName = "TestField",
            ChangedAt = DateTime.UtcNow,
            ChangedBy = "TestUser"
        };

        // Act
        var result = await _historyService.AddHistoryEntryAsync(entry);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task AddHistoryBatchAsync_WithValidBatch_ReturnsSuccessResult()
    {
        // Arrange
        var entries = CreateTestEntries(500);
        var batch = HistoryBatch.Create(entries, "TestUser", "Integration test batch");

        // Act
        var result = await _historyService.AddHistoryBatchAsync(batch);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.SuccessCount.Should().Be(500);
        result.FailureCount.Should().Be(0);
        result.TotalCount.Should().Be(500);
        result.ProcessingTimeMs.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task AddHistoryBatchAsync_WithMixedValidInvalidEntries_ReturnsPartialSuccess()
    {
        // Arrange
        var validEntries = CreateTestEntries(300);
        var invalidEntries = new List<HistoryEntry>
        {
            new() { LeadId = 0, FieldName = "Invalid", ChangedBy = "Test" }, // Invalid LeadId
            new() { LeadId = 1, FieldName = "", ChangedBy = "Test" }, // Empty FieldName
        };

        var allEntries = validEntries.Concat(invalidEntries);
        var batch = HistoryBatch.Create(allEntries, "TestUser", "Mixed batch test");

        // Act
        var result = await _historyService.AddHistoryBatchAsync(batch);

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeFalse(); // Should fail due to invalid entries
        result.SuccessCount.Should().Be(300);
        result.FailureCount.Should().Be(2);
        result.TotalCount.Should().Be(302);
    }

    [Fact]
    public async Task GetStorageStatsAsync_ReturnsValidStatistics()
    {
        // Arrange
        var testEntries = CreateTestEntriesAcrossTiers();
        await _historyService.AddHistoryBatchAsync(testEntries);

        // Act
        var stats = await _historyService.GetStorageStatsAsync();

        // Assert
        stats.Should().NotBeNull();
        stats.TierStats.Should().HaveCount(3);
        stats.TierStats.Should().ContainKey(StorageTier.Hot);
        stats.TierStats.Should().ContainKey(StorageTier.Warm);
        stats.TierStats.Should().ContainKey(StorageTier.Cold);
        stats.TotalEntries.Should().BeGreaterThan(0);
        stats.CollectedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
    }

    [Fact]
    public async Task ValidateDataIntegrityAsync_WithValidData_ReturnsValidResult()
    {
        // Arrange
        var testEntries = CreateTestEntries(1000);
        await _historyService.AddHistoryBatchAsync(testEntries);

        // Act
        var result = await _historyService.ValidateDataIntegrityAsync();

        // Assert
        result.Should().NotBeNull();
        result.IsValid.Should().BeTrue();
        result.Issues.Should().BeEmpty();
        result.RecordsValidated.Should().NotBeEmpty();
        result.ValidationTimeMs.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task ArchiveOldEntriesAsync_WithOldEntries_ReturnsArchivedCount()
    {
        // Arrange
        var testEntries = CreateTestEntriesAcrossTiers();
        await _historyService.AddHistoryBatchAsync(testEntries);

        var cutoffDate = DateTime.UtcNow.AddDays(-100);

        // Act
        var archivedCount = await _historyService.ArchiveOldEntriesAsync(cutoffDate);

        // Assert
        archivedCount.Should().BeGreaterThanOrEqualTo(0);
    }

    [Fact]
    public async Task ClearCacheAsync_WithValidParameters_ReturnsTrue()
    {
        // Arrange
        var testEntries = CreateTestEntries(100);
        await _historyService.AddHistoryBatchAsync(testEntries);

        // Perform a query to populate cache
        var query = new HistoryQuery { LeadId = 1, Page = 1, PageSize = 50 };
        await _historyService.GetHistoryAsync(query);

        // Act
        var result = await _historyService.ClearCacheAsync(leadId: 1);

        // Assert
        result.Should().BeTrue();
    }

    private List<HistoryEntry> CreateTestEntries(int count)
    {
        var entries = new List<HistoryEntry>();
        var random = new Random(42); // Fixed seed for reproducible tests

        for (int i = 0; i < count; i++)
        {
            entries.Add(new HistoryEntry
            {
                LeadId = random.Next(1, 10),
                FieldName = $"Field{random.Next(1, 5)}",
                OldValue = $"OldValue{i}",
                NewValue = $"NewValue{i}",
                ChangedAt = DateTime.UtcNow.AddDays(-random.Next(0, 30)), // Recent entries (hot tier)
                ChangedBy = $"User{random.Next(1, 5)}"
            });
        }

        return entries;
    }

    private List<HistoryEntry> CreateTestEntriesAcrossTiers()
    {
        var entries = new List<HistoryEntry>();
        var random = new Random(42);

        // Hot tier entries (0-90 days old)
        for (int i = 0; i < 100; i++)
        {
            entries.Add(new HistoryEntry
            {
                LeadId = random.Next(1, 10),
                FieldName = $"HotField{i % 5}",
                OldValue = $"HotOld{i}",
                NewValue = $"HotNew{i}",
                ChangedAt = DateTime.UtcNow.AddDays(-random.Next(0, 90)),
                ChangedBy = "HotUser"
            });
        }

        // Warm tier entries (91-365 days old)
        for (int i = 0; i < 100; i++)
        {
            entries.Add(new HistoryEntry
            {
                LeadId = random.Next(1, 10),
                FieldName = $"WarmField{i % 5}",
                OldValue = $"WarmOld{i}",
                NewValue = $"WarmNew{i}",
                ChangedAt = DateTime.UtcNow.AddDays(-random.Next(91, 365)),
                ChangedBy = "WarmUser"
            });
        }

        // Cold tier entries (365+ days old)
        for (int i = 0; i < 100; i++)
        {
            entries.Add(new HistoryEntry
            {
                LeadId = random.Next(1, 10),
                FieldName = $"ColdField{i % 5}",
                OldValue = $"ColdOld{i}",
                NewValue = $"ColdNew{i}",
                ChangedAt = DateTime.UtcNow.AddDays(-random.Next(366, 500)),
                ChangedBy = "ColdUser"
            });
        }

        return entries;
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

// Mock tier storage for testing
public class MockTierStorage : ITierStorage
{
    private readonly List<HistoryEntry> _entries = new();
    private readonly ILogger<MockTierStorage> _logger;

    public StorageTier Tier { get; }

    public MockTierStorage(StorageTier tier, ILogger<MockTierStorage> logger)
    {
        Tier = tier;
        _logger = logger;
    }

    public Task<HistoryResult<HistoryEntry>> QueryAsync(HistoryQuery query, CancellationToken cancellationToken = default)
    {
        var filteredEntries = _entries.AsQueryable();

        if (query.LeadId.HasValue)
            filteredEntries = filteredEntries.Where(e => e.LeadId == query.LeadId.Value);

        if (!string.IsNullOrEmpty(query.FieldName))
            filteredEntries = filteredEntries.Where(e => e.FieldName == query.FieldName);

        if (!string.IsNullOrEmpty(query.ChangedBy))
            filteredEntries = filteredEntries.Where(e => e.ChangedBy == query.ChangedBy);

        if (query.StartDate.HasValue)
            filteredEntries = filteredEntries.Where(e => e.ChangedAt >= query.StartDate.Value);

        if (query.EndDate.HasValue)
            filteredEntries = filteredEntries.Where(e => e.ChangedAt <= query.EndDate.Value);

        var totalCount = filteredEntries.Count();
        var pagedEntries = filteredEntries
            .Skip(query.GetSkipCount())
            .Take(query.PageSize)
            .ToList();

        var result = HistoryResult<HistoryEntry>.Success(pagedEntries, totalCount, query.Page, query.PageSize, 10);
        result.QueriedTiers = new[] { Tier };

        return Task.FromResult(result);
    }

    public Task<bool> AddEntriesAsync(IEnumerable<HistoryEntry> entries, CancellationToken cancellationToken = default)
    {
        var tierEntries = entries.Where(e => e.GetStorageTier() == Tier).ToList();
        _entries.AddRange(tierEntries);
        return Task.FromResult(true);
    }

    public Task<int> MoveEntriesAsync(DateTime cutoffDate, StorageTier targetTier, CancellationToken cancellationToken = default)
    {
        var entriesToMove = _entries.Where(e => e.ChangedAt < cutoffDate).ToList();
        foreach (var entry in entriesToMove)
        {
            _entries.Remove(entry);
        }
        return Task.FromResult(entriesToMove.Count);
    }

    public Task<TierStorageStats> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(new TierStorageStats
        {
            Tier = Tier,
            EntryCount = _entries.Count,
            SizeBytes = _entries.Count * 500, // Estimate
            AverageQueryTimeMs = 25.0,
            EstimatedMonthlyCost = _entries.Count * 0.001m,
            OldestEntry = _entries.Any() ? _entries.Min(e => e.ChangedAt) : null,
            NewestEntry = _entries.Any() ? _entries.Max(e => e.ChangedAt) : null
        });
    }
}
