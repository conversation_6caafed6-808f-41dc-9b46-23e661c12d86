-- =============================================
-- CRM History System - Data Migration Script
-- Version: 1.0.0
-- Description: Migrates existing JSON-based history data to new relational structure
-- =============================================

-- Migration configuration
DECLARE @BatchSize INT = 10000;
DECLARE @MaxBatches INT = 1000;
DECLARE @DelayBetweenBatches VARCHAR(8) = '00:00:02'; -- 2 seconds
DECLARE @LogProgress BIT = 1;

-- Migration tracking table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'HistoryMigrationLog')
BEGIN
    CREATE TABLE [dbo].[HistoryMigrationLog] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [BatchNumber] INT NOT NULL,
        [StartTime] DATETIME2 NOT NULL,
        [EndTime] DATETIME2 NULL,
        [RecordsProcessed] INT NOT NULL DEFAULT(0),
        [RecordsSuccessful] INT NOT NULL DEFAULT(0),
        [RecordsFailed] INT NOT NULL DEFAULT(0),
        [ErrorMessage] NVARCHAR(MAX) NULL,
        [Status] NVARCHAR(20) NOT NULL DEFAULT('InProgress') -- InProgress, Completed, Failed
    );
END;

-- Migration state tracking
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'HistoryMigrationState')
BEGIN
    CREATE TABLE [dbo].[HistoryMigrationState] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [MigrationName] NVARCHAR(100) NOT NULL UNIQUE,
        [LastProcessedId] BIGINT NOT NULL DEFAULT(0),
        [TotalRecords] BIGINT NOT NULL DEFAULT(0),
        [ProcessedRecords] BIGINT NOT NULL DEFAULT(0),
        [StartTime] DATETIME2 NOT NULL DEFAULT(GETUTCDATE()),
        [LastUpdateTime] DATETIME2 NOT NULL DEFAULT(GETUTCDATE()),
        [Status] NVARCHAR(20) NOT NULL DEFAULT('NotStarted') -- NotStarted, InProgress, Completed, Failed
    );
END;

-- =============================================
-- Main Migration Procedure
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_MigrateHistoryData]
    @SourceTable NVARCHAR(128) = 'LegacyHistoryTable', -- Replace with actual source table name
    @StartFromId BIGINT = 0,
    @BatchSize INT = 10000,
    @MaxBatches INT = 1000,
    @DryRun BIT = 0 -- Set to 1 for validation without actual migration
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    DECLARE @CurrentBatch INT = 1;
    DECLARE @ProcessedRecords BIGINT = 0;
    DECLARE @TotalRecords BIGINT = 0;
    DECLARE @LastProcessedId BIGINT = @StartFromId;
    DECLARE @BatchStartTime DATETIME2;
    DECLARE @BatchEndTime DATETIME2;
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @MigrationName NVARCHAR(100) = 'JSONToRelational_' + FORMAT(GETUTCDATE(), 'yyyyMMdd_HHmmss');
    
    BEGIN TRY
        -- Initialize migration state
        INSERT INTO [dbo].[HistoryMigrationState] 
        ([MigrationName], [LastProcessedId], [Status])
        VALUES (@MigrationName, @StartFromId, 'InProgress');
        
        -- Get total record count for progress tracking
        DECLARE @CountSQL NVARCHAR(MAX) = N'SELECT @TotalRecords = COUNT(*) FROM [' + @SourceTable + '] WHERE Id > @StartFromId';
        EXEC sp_executesql @CountSQL, N'@TotalRecords BIGINT OUTPUT, @StartFromId BIGINT', @TotalRecords OUTPUT, @StartFromId;
        
        UPDATE [dbo].[HistoryMigrationState] 
        SET [TotalRecords] = @TotalRecords 
        WHERE [MigrationName] = @MigrationName;
        
        PRINT 'Starting migration of ' + CAST(@TotalRecords AS NVARCHAR(20)) + ' records from ' + @SourceTable;
        PRINT 'Batch size: ' + CAST(@BatchSize AS NVARCHAR(10)) + ', Max batches: ' + CAST(@MaxBatches AS NVARCHAR(10));
        PRINT 'Dry run mode: ' + CASE WHEN @DryRun = 1 THEN 'YES' ELSE 'NO' END;
        PRINT '----------------------------------------';
        
        -- Process batches
        WHILE @CurrentBatch <= @MaxBatches
        BEGIN
            SET @BatchStartTime = GETUTCDATE();
            
            -- Log batch start
            INSERT INTO [dbo].[HistoryMigrationLog] 
            ([BatchNumber], [StartTime], [RecordsProcessed])
            VALUES (@CurrentBatch, @BatchStartTime, 0);
            
            DECLARE @LogId INT = SCOPE_IDENTITY();
            
            -- Process current batch
            DECLARE @BatchProcessed INT = 0;
            DECLARE @BatchSuccessful INT = 0;
            DECLARE @BatchFailed INT = 0;
            
            BEGIN TRANSACTION;
            
            BEGIN TRY
                -- Dynamic SQL to handle different source table structures
                -- This example assumes a JSON column named 'HistoryData'
                DECLARE @MigrationSQL NVARCHAR(MAX) = N'
                WITH SourceData AS (
                    SELECT TOP (@BatchSize)
                        Id,
                        HistoryData,
                        ROW_NUMBER() OVER (ORDER BY Id) as RowNum
                    FROM [' + @SourceTable + ']
                    WHERE Id > @LastProcessedId
                    ORDER BY Id
                ),
                ParsedData AS (
                    SELECT 
                        Id,
                        JSON_VALUE(HistoryData, ''$.leadId'') as LeadId,
                        JSON_VALUE(HistoryData, ''$.fieldName'') as FieldName,
                        JSON_VALUE(HistoryData, ''$.oldValue'') as OldValue,
                        JSON_VALUE(HistoryData, ''$.newValue'') as NewValue,
                        TRY_CAST(JSON_VALUE(HistoryData, ''$.changedAt'') AS DATETIME2(3)) as ChangedAt,
                        JSON_VALUE(HistoryData, ''$.changedBy'') as ChangedBy,
                        JSON_VALUE(HistoryData, ''$.metadata'') as Metadata,
                        RowNum
                    FROM SourceData
                )
                SELECT 
                    @BatchProcessed = COUNT(*),
                    @LastProcessedId = MAX(Id)
                FROM ParsedData;
                
                -- Insert into appropriate tier based on date
                INSERT INTO [dbo].[HistoryEntries_Hot] 
                ([LeadId], [FieldName], [OldValue], [NewValue], [ChangedAt], [ChangedBy], [Metadata])
                SELECT 
                    TRY_CAST(LeadId AS INT),
                    FieldName,
                    OldValue,
                    NewValue,
                    ISNULL(ChangedAt, GETUTCDATE()),
                    ChangedBy,
                    Metadata
                FROM ParsedData
                WHERE TRY_CAST(LeadId AS INT) IS NOT NULL
                  AND FieldName IS NOT NULL
                  AND ChangedBy IS NOT NULL
                  AND ISNULL(ChangedAt, GETUTCDATE()) >= DATEADD(day, -90, GETUTCDATE())
                  AND @DryRun = 0;
                
                INSERT INTO [dbo].[HistoryEntries_Warm] 
                ([LeadId], [FieldName], [OldValue], [NewValue], [ChangedAt], [ChangedBy], [Metadata])
                SELECT 
                    TRY_CAST(LeadId AS INT),
                    FieldName,
                    OldValue,
                    NewValue,
                    ISNULL(ChangedAt, GETUTCDATE()),
                    ChangedBy,
                    Metadata
                FROM ParsedData
                WHERE TRY_CAST(LeadId AS INT) IS NOT NULL
                  AND FieldName IS NOT NULL
                  AND ChangedBy IS NOT NULL
                  AND ISNULL(ChangedAt, GETUTCDATE()) < DATEADD(day, -90, GETUTCDATE())
                  AND ISNULL(ChangedAt, GETUTCDATE()) >= DATEADD(day, -365, GETUTCDATE())
                  AND @DryRun = 0;
                
                SET @BatchSuccessful = @@ROWCOUNT;';
                
                EXEC sp_executesql @MigrationSQL, 
                    N'@BatchSize INT, @LastProcessedId BIGINT OUTPUT, @BatchProcessed INT OUTPUT, @BatchSuccessful INT OUTPUT, @DryRun BIT',
                    @BatchSize, @LastProcessedId OUTPUT, @BatchProcessed OUTPUT, @BatchSuccessful OUTPUT, @DryRun;
                
                COMMIT TRANSACTION;
                
                SET @BatchEndTime = GETUTCDATE();
                SET @ProcessedRecords = @ProcessedRecords + @BatchProcessed;
                
                -- Update batch log
                UPDATE [dbo].[HistoryMigrationLog] 
                SET [EndTime] = @BatchEndTime,
                    [RecordsProcessed] = @BatchProcessed,
                    [RecordsSuccessful] = @BatchSuccessful,
                    [RecordsFailed] = @BatchProcessed - @BatchSuccessful,
                    [Status] = 'Completed'
                WHERE [Id] = @LogId;
                
                -- Update migration state
                UPDATE [dbo].[HistoryMigrationState] 
                SET [LastProcessedId] = @LastProcessedId,
                    [ProcessedRecords] = @ProcessedRecords,
                    [LastUpdateTime] = GETUTCDATE()
                WHERE [MigrationName] = @MigrationName;
                
                -- Progress reporting
                DECLARE @ProgressPercent DECIMAL(5,2) = 
                    CASE WHEN @TotalRecords > 0 
                         THEN (@ProcessedRecords * 100.0) / @TotalRecords 
                         ELSE 0 END;
                
                PRINT 'Batch ' + CAST(@CurrentBatch AS NVARCHAR(10)) + 
                      ': Processed ' + CAST(@BatchProcessed AS NVARCHAR(10)) + 
                      ' records (' + CAST(@ProgressPercent AS NVARCHAR(10)) + '% complete)' +
                      ' in ' + CAST(DATEDIFF(MILLISECOND, @BatchStartTime, @BatchEndTime) AS NVARCHAR(10)) + 'ms';
                
                -- Exit if no more records to process
                IF @BatchProcessed = 0
                    BREAK;
                
                -- Delay between batches to reduce system load
                IF @CurrentBatch < @MaxBatches
                    WAITFOR DELAY '00:00:02';
                
            END TRY
            BEGIN CATCH
                IF @@TRANCOUNT > 0
                    ROLLBACK TRANSACTION;
                
                SET @ErrorMessage = ERROR_MESSAGE();
                SET @BatchEndTime = GETUTCDATE();
                
                -- Log batch error
                UPDATE [dbo].[HistoryMigrationLog] 
                SET [EndTime] = @BatchEndTime,
                    [ErrorMessage] = @ErrorMessage,
                    [Status] = 'Failed'
                WHERE [Id] = @LogId;
                
                PRINT 'Batch ' + CAST(@CurrentBatch AS NVARCHAR(10)) + ' failed: ' + @ErrorMessage;
                
                -- Continue with next batch for non-critical errors
                IF ERROR_SEVERITY() >= 16
                    THROW;
            END CATCH
            
            SET @CurrentBatch = @CurrentBatch + 1;
        END
        
        -- Mark migration as completed
        UPDATE [dbo].[HistoryMigrationState] 
        SET [Status] = 'Completed',
            [LastUpdateTime] = GETUTCDATE()
        WHERE [MigrationName] = @MigrationName;
        
        PRINT '----------------------------------------';
        PRINT 'Migration completed successfully!';
        PRINT 'Total records processed: ' + CAST(@ProcessedRecords AS NVARCHAR(20));
        PRINT 'Migration name: ' + @MigrationName;
        
    END TRY
    BEGIN CATCH
        -- Mark migration as failed
        UPDATE [dbo].[HistoryMigrationState] 
        SET [Status] = 'Failed',
            [LastUpdateTime] = GETUTCDATE()
        WHERE [MigrationName] = @MigrationName;
        
        SET @ErrorMessage = ERROR_MESSAGE();
        PRINT 'Migration failed: ' + @ErrorMessage;
        THROW;
    END CATCH
END;

-- =============================================
-- Data Validation Procedure
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_ValidateMigratedData]
    @SourceTable NVARCHAR(128) = 'LegacyHistoryTable'
AS
BEGIN
    SET NOCOUNT ON;
    
    PRINT 'Starting data validation...';
    PRINT '----------------------------------------';
    
    -- Count comparison
    DECLARE @SourceCount BIGINT;
    DECLARE @TargetHotCount BIGINT;
    DECLARE @TargetWarmCount BIGINT;
    DECLARE @TotalTargetCount BIGINT;
    
    DECLARE @CountSQL NVARCHAR(MAX) = N'SELECT @SourceCount = COUNT(*) FROM [' + @SourceTable + ']';
    EXEC sp_executesql @CountSQL, N'@SourceCount BIGINT OUTPUT', @SourceCount OUTPUT;
    
    SELECT @TargetHotCount = COUNT(*) FROM [dbo].[HistoryEntries_Hot];
    SELECT @TargetWarmCount = COUNT(*) FROM [dbo].[HistoryEntries_Warm];
    SET @TotalTargetCount = @TargetHotCount + @TargetWarmCount;
    
    PRINT 'Record count validation:';
    PRINT '  Source table: ' + CAST(@SourceCount AS NVARCHAR(20));
    PRINT '  Hot tier: ' + CAST(@TargetHotCount AS NVARCHAR(20));
    PRINT '  Warm tier: ' + CAST(@TargetWarmCount AS NVARCHAR(20));
    PRINT '  Total target: ' + CAST(@TotalTargetCount AS NVARCHAR(20));
    PRINT '  Difference: ' + CAST((@SourceCount - @TotalTargetCount) AS NVARCHAR(20));
    
    -- Data integrity checks
    PRINT '';
    PRINT 'Data integrity validation:';
    
    -- Check for required fields
    DECLARE @InvalidRecords INT;
    
    SELECT @InvalidRecords = COUNT(*) 
    FROM [dbo].[HistoryEntries_Hot] 
    WHERE [LeadId] <= 0 OR [FieldName] = '' OR [ChangedBy] = '';
    
    PRINT '  Hot tier invalid records: ' + CAST(@InvalidRecords AS NVARCHAR(10));
    
    SELECT @InvalidRecords = COUNT(*) 
    FROM [dbo].[HistoryEntries_Warm] 
    WHERE [LeadId] <= 0 OR [FieldName] = '' OR [ChangedBy] = '';
    
    PRINT '  Warm tier invalid records: ' + CAST(@InvalidRecords AS NVARCHAR(10));
    
    -- Check date distribution
    PRINT '';
    PRINT 'Date distribution validation:';
    
    SELECT 
        'Hot tier date range: ' + 
        ISNULL(CAST(MIN([ChangedAt]) AS NVARCHAR(20)), 'NULL') + ' to ' + 
        ISNULL(CAST(MAX([ChangedAt]) AS NVARCHAR(20)), 'NULL') as DateRange
    FROM [dbo].[HistoryEntries_Hot];
    
    SELECT 
        'Warm tier date range: ' + 
        ISNULL(CAST(MIN([ChangedAt]) AS NVARCHAR(20)), 'NULL') + ' to ' + 
        ISNULL(CAST(MAX([ChangedAt]) AS NVARCHAR(20)), 'NULL') as DateRange
    FROM [dbo].[HistoryEntries_Warm];
    
    PRINT '----------------------------------------';
    PRINT 'Data validation completed.';
END;

-- =============================================

PRINT 'Data migration procedures created successfully:';
PRINT '  - sp_MigrateHistoryData: Main migration procedure';
PRINT '  - sp_ValidateMigratedData: Data validation procedure';
PRINT '';
PRINT 'Usage examples:';
PRINT '  -- Dry run validation:';
PRINT '  EXEC sp_MigrateHistoryData @SourceTable = ''YourLegacyTable'', @DryRun = 1;';
PRINT '';
PRINT '  -- Actual migration:';
PRINT '  EXEC sp_MigrateHistoryData @SourceTable = ''YourLegacyTable'', @BatchSize = 10000;';
PRINT '';
PRINT '  -- Validate results:';
PRINT '  EXEC sp_ValidateMigratedData @SourceTable = ''YourLegacyTable'';';
