using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using System.IO.Compression;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Core.Configuration;
using System.Diagnostics;

namespace CrmHistorySystem.Infrastructure.Storage;

/// <summary>
/// Azure Blob Storage implementation for cold tier storage.
/// Optimized for cost-effective long-term storage with compression.
/// </summary>
public class BlobStorageTierStorage : ITierStorage
{
    private readonly BlobServiceClient _blobServiceClient;
    private readonly ILogger<BlobStorageTierStorage> _logger;
    private readonly HistoryOptions _options;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly string _containerName = "history-cold-tier";

    public StorageTier Tier => StorageTier.Cold;

    public BlobStorageTierStorage(
        BlobServiceClient blobServiceClient,
        ILogger<BlobStorageTierStorage> logger,
        IOptions<HistoryOptions> options)
    {
        _blobServiceClient = blobServiceClient;
        _logger = logger;
        _options = options.Value;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false
        };
    }

    /// <summary>
    /// Queries history entries from cold storage.
    /// Note: This is slower than SQL queries but cost-effective for archival data.
    /// </summary>
    public async Task<HistoryResult<HistoryEntry>> QueryAsync(
        HistoryQuery query, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var containerClient = await GetContainerClientAsync();
            var allEntries = new List<HistoryEntry>();

            // Get blob names that match the query date range
            var blobNames = await GetRelevantBlobNamesAsync(containerClient, query, cancellationToken);
            
            // Process each blob
            foreach (var blobName in blobNames)
            {
                var entries = await ReadEntriesFromBlobAsync(containerClient, blobName, cancellationToken);
                var filteredEntries = FilterEntries(entries, query);
                allEntries.AddRange(filteredEntries);
            }

            // Apply sorting and pagination in memory (not ideal for large datasets)
            var sortedEntries = ApplySorting(allEntries, query.SortOrder);
            var totalCount = sortedEntries.Count;
            
            var pagedEntries = sortedEntries
                .Skip(query.GetSkipCount())
                .Take(query.PageSize)
                .ToList();

            stopwatch.Stop();

            var result = HistoryResult<HistoryEntry>.Success(
                pagedEntries,
                totalCount,
                query.Page,
                query.PageSize,
                stopwatch.ElapsedMilliseconds);

            result.QueriedTiers = new[] { Tier };

            _logger.LogDebug("Cold tier query completed: {Count} results from {BlobCount} blobs in {ElapsedMs}ms", 
                pagedEntries.Count, blobNames.Count(), stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error querying cold tier: {Query}", query);
            throw;
        }
    }

    /// <summary>
    /// Adds history entries to cold storage.
    /// Entries are compressed and stored in monthly blob files.
    /// </summary>
    public async Task<bool> AddEntriesAsync(
        IEnumerable<HistoryEntry> entries, 
        CancellationToken cancellationToken = default)
    {
        var entryList = entries.ToList();
        if (!entryList.Any())
            return true;

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var containerClient = await GetContainerClientAsync();
            
            // Group entries by month for efficient storage
            var entriesByMonth = entryList
                .GroupBy(e => new { e.ChangedAt.Year, e.ChangedAt.Month })
                .ToList();

            var totalProcessed = 0;

            foreach (var monthGroup in entriesByMonth)
            {
                var blobName = GenerateBlobName(monthGroup.Key.Year, monthGroup.Key.Month);
                var processed = await AppendEntriesToBlobAsync(
                    containerClient, 
                    blobName, 
                    monthGroup.ToList(), 
                    cancellationToken);
                
                totalProcessed += processed;
            }

            stopwatch.Stop();
            
            _logger.LogDebug("Added {Count} entries to cold tier across {BlobCount} blobs in {ElapsedMs}ms", 
                totalProcessed, entriesByMonth.Count, stopwatch.ElapsedMilliseconds);

            return totalProcessed == entryList.Count;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error adding {Count} entries to cold tier", entryList.Count);
            throw;
        }
    }

    /// <summary>
    /// Cold tier doesn't support moving entries (they're already archived).
    /// </summary>
    public async Task<int> MoveEntriesAsync(
        DateTime cutoffDate, 
        StorageTier targetTier, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogWarning("Move operation not supported for cold tier storage");
        await Task.CompletedTask;
        return 0;
    }

    /// <summary>
    /// Gets storage statistics for cold tier.
    /// </summary>
    public async Task<TierStorageStats> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var containerClient = await GetContainerClientAsync();
            
            long totalSize = 0;
            long entryCount = 0;
            DateTime? oldestEntry = null;
            DateTime? newestEntry = null;

            await foreach (var blobItem in containerClient.GetBlobsAsync(cancellationToken: cancellationToken))
            {
                totalSize += blobItem.Properties.ContentLength ?? 0;
                
                // Extract date from blob name to estimate entry count and date range
                if (TryParseBlobDate(blobItem.Name, out var blobDate))
                {
                    if (!oldestEntry.HasValue || blobDate < oldestEntry)
                        oldestEntry = blobDate;
                    
                    if (!newestEntry.HasValue || blobDate > newestEntry)
                        newestEntry = blobDate;
                    
                    // Estimate entries per blob (rough calculation)
                    entryCount += EstimateEntriesInBlob(blobItem.Properties.ContentLength ?? 0);
                }
            }

            return new TierStorageStats
            {
                Tier = Tier,
                EntryCount = entryCount,
                SizeBytes = totalSize,
                OldestEntry = oldestEntry,
                NewestEntry = newestEntry,
                AverageQueryTimeMs = 500.0, // Cold tier is slower
                EstimatedMonthlyCost = CalculateBlobStorageCost(totalSize)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cold tier storage stats");
            throw;
        }
    }

    private async Task<BlobContainerClient> GetContainerClientAsync()
    {
        var containerClient = _blobServiceClient.GetBlobContainerClient(_containerName);
        await containerClient.CreateIfNotExistsAsync(PublicAccessType.None);
        return containerClient;
    }

    private async Task<IEnumerable<string>> GetRelevantBlobNamesAsync(
        BlobContainerClient containerClient, 
        HistoryQuery query, 
        CancellationToken cancellationToken)
    {
        var blobNames = new List<string>();
        
        await foreach (var blobItem in containerClient.GetBlobsAsync(cancellationToken: cancellationToken))
        {
            if (IsBlobRelevantForQuery(blobItem.Name, query))
            {
                blobNames.Add(blobItem.Name);
            }
        }

        return blobNames;
    }

    private async Task<List<HistoryEntry>> ReadEntriesFromBlobAsync(
        BlobContainerClient containerClient, 
        string blobName, 
        CancellationToken cancellationToken)
    {
        try
        {
            var blobClient = containerClient.GetBlobClient(blobName);
            
            using var response = await blobClient.OpenReadAsync(cancellationToken: cancellationToken);
            using var gzipStream = new GZipStream(response, CompressionMode.Decompress);
            
            var entries = await JsonSerializer.DeserializeAsync<List<HistoryEntry>>(
                gzipStream, _jsonOptions, cancellationToken);
            
            return entries ?? new List<HistoryEntry>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading entries from blob: {BlobName}", blobName);
            return new List<HistoryEntry>();
        }
    }

    private async Task<int> AppendEntriesToBlobAsync(
        BlobContainerClient containerClient, 
        string blobName, 
        List<HistoryEntry> entries, 
        CancellationToken cancellationToken)
    {
        try
        {
            var blobClient = containerClient.GetBlobClient(blobName);
            
            // Read existing entries if blob exists
            var existingEntries = new List<HistoryEntry>();
            if (await blobClient.ExistsAsync(cancellationToken))
            {
                existingEntries = await ReadEntriesFromBlobAsync(containerClient, blobName, cancellationToken);
            }

            // Combine with new entries
            existingEntries.AddRange(entries);

            // Serialize and compress
            using var memoryStream = new MemoryStream();
            using (var gzipStream = new GZipStream(memoryStream, CompressionLevel.Optimal))
            {
                await JsonSerializer.SerializeAsync(gzipStream, existingEntries, _jsonOptions, cancellationToken);
            }

            // Upload compressed data
            memoryStream.Position = 0;
            await blobClient.UploadAsync(memoryStream, overwrite: true, cancellationToken);

            return entries.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error appending entries to blob: {BlobName}", blobName);
            throw;
        }
    }

    private static string GenerateBlobName(int year, int month)
    {
        return $"history-{year:D4}-{month:D2}.json.gz";
    }

    private static bool IsBlobRelevantForQuery(string blobName, HistoryQuery query)
    {
        if (!TryParseBlobDate(blobName, out var blobDate))
            return false;

        if (query.StartDate.HasValue && blobDate < query.StartDate.Value.Date)
            return false;

        if (query.EndDate.HasValue && blobDate > query.EndDate.Value.Date)
            return false;

        return true;
    }

    private static bool TryParseBlobDate(string blobName, out DateTime date)
    {
        date = default;
        
        // Expected format: history-YYYY-MM.json.gz
        var parts = blobName.Replace("history-", "").Replace(".json.gz", "").Split('-');
        
        if (parts.Length == 2 && 
            int.TryParse(parts[0], out var year) && 
            int.TryParse(parts[1], out var month))
        {
            date = new DateTime(year, month, 1);
            return true;
        }

        return false;
    }

    private static List<HistoryEntry> FilterEntries(List<HistoryEntry> entries, HistoryQuery query)
    {
        return entries.Where(e =>
            (!query.LeadId.HasValue || e.LeadId == query.LeadId.Value) &&
            (string.IsNullOrEmpty(query.FieldName) || e.FieldName == query.FieldName) &&
            (string.IsNullOrEmpty(query.ChangedBy) || e.ChangedBy == query.ChangedBy) &&
            (!query.StartDate.HasValue || e.ChangedAt >= query.StartDate.Value) &&
            (!query.EndDate.HasValue || e.ChangedAt <= query.EndDate.Value)
        ).ToList();
    }

    private static List<HistoryEntry> ApplySorting(List<HistoryEntry> entries, HistorySortOrder sortOrder)
    {
        return sortOrder switch
        {
            HistorySortOrder.ChangedAtAscending => entries.OrderBy(e => e.ChangedAt).ToList(),
            HistorySortOrder.FieldNameThenChangedAt => entries.OrderBy(e => e.FieldName).ThenByDescending(e => e.ChangedAt).ToList(),
            HistorySortOrder.ChangedByThenChangedAt => entries.OrderBy(e => e.ChangedBy).ThenByDescending(e => e.ChangedAt).ToList(),
            _ => entries.OrderByDescending(e => e.ChangedAt).ToList()
        };
    }

    private static long EstimateEntriesInBlob(long blobSize)
    {
        // Rough estimate: compressed JSON entries are about 200 bytes each
        return blobSize / 200;
    }

    private static decimal CalculateBlobStorageCost(long totalSizeBytes)
    {
        // Azure Blob Storage cool tier pricing (rough estimate)
        var sizeGB = totalSizeBytes / (1024.0 * 1024.0 * 1024.0);
        return (decimal)(sizeGB * 0.01); // $0.01 per GB per month
    }
}
