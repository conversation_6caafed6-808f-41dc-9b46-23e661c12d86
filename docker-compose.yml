version: '3.8'

services:
  # CRM History API
  crm-history-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crm-history-api
    ports:
      - "8080:80"
      - "8443:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__Hot=Server=sqlserver-hot;Database=CrmHistory_Hot;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true
      - ConnectionStrings__Warm=Server=sqlserver-warm;Database=CrmHistory_Warm;User Id=sa;Password=YourStrong@Passw0rd;TrustServerCertificate=true
      - ConnectionStrings__Cold=DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://azurite:10000/devstoreaccount1
      - ConnectionStrings__Redis=redis:6379
      - History__Cache__UseDistributedCache=true
      - History__Performance__Enabled=true
      - Serilog__WriteTo__1__Args__path=/app/logs/crm-history-.txt
    volumes:
      - ./logs:/app/logs
    depends_on:
      - sqlserver-hot
      - sqlserver-warm
      - redis
      - azurite
    networks:
      - crm-history-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # SQL Server for Hot Tier
  sqlserver-hot:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: sqlserver-hot
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Developer
    ports:
      - "1433:1433"
    volumes:
      - sqlserver-hot-data:/var/opt/mssql
      - ./scripts/init-hot-db.sql:/docker-entrypoint-initdb.d/init-hot-db.sql
    networks:
      - crm-history-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # SQL Server for Warm Tier
  sqlserver-warm:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: sqlserver-warm
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Developer
    ports:
      - "1434:1433"
    volumes:
      - sqlserver-warm-data:/var/opt/mssql
      - ./scripts/init-warm-db.sql:/docker-entrypoint-initdb.d/init-warm-db.sql
    networks:
      - crm-history-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P YourStrong@Passw0rd -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: redis-cache
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./docker/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - crm-history-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Azurite (Azure Storage Emulator) for Cold Tier
  azurite:
    image: mcr.microsoft.com/azure-storage/azurite:latest
    container_name: azurite-storage
    ports:
      - "10000:10000"  # Blob service
      - "10001:10001"  # Queue service
      - "10002:10002"  # Table service
    volumes:
      - azurite-data:/data
    command: azurite --blobHost 0.0.0.0 --queueHost 0.0.0.0 --tableHost 0.0.0.0 --location /data --debug /data/debug.log
    networks:
      - crm-history-network
    restart: unless-stopped

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - crm-history-network
    restart: unless-stopped

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./docker/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - crm-history-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/ssl:/etc/nginx/ssl
    depends_on:
      - crm-history-api
    networks:
      - crm-history-network
    restart: unless-stopped

volumes:
  sqlserver-hot-data:
    driver: local
  sqlserver-warm-data:
    driver: local
  redis-data:
    driver: local
  azurite-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  crm-history-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
