using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Core.Models;

/// <summary>
/// Represents a query for retrieving history entries with filtering and pagination support.
/// Optimized for efficient database queries across tiered storage.
/// </summary>
public class HistoryQuery
{
    /// <summary>
    /// Filter by specific lead ID. When specified, only history for this lead is returned.
    /// </summary>
    public int? LeadId { get; set; }

    /// <summary>
    /// Filter by specific field name. Supports exact match only for optimal index usage.
    /// </summary>
    [StringLength(100)]
    public string? FieldName { get; set; }

    /// <summary>
    /// Filter by user who made the change.
    /// </summary>
    [StringLength(100)]
    public string? ChangedBy { get; set; }

    /// <summary>
    /// Start date for the query range (inclusive).
    /// Used for tier routing and query optimization.
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// End date for the query range (inclusive).
    /// Used for tier routing and query optimization.
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Page number for pagination (1-based).
    /// </summary>
    [Range(1, int.MaxValue)]
    public int Page { get; set; } = 1;

    /// <summary>
    /// Number of records per page. Limited to prevent performance issues.
    /// </summary>
    [Range(1, 1000)]
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// Sort order for results. Default is by ChangedAt descending (newest first).
    /// </summary>
    public HistorySortOrder SortOrder { get; set; } = HistorySortOrder.ChangedAtDescending;

    /// <summary>
    /// Whether to include metadata in the results. 
    /// Set to false for better performance when metadata is not needed.
    /// </summary>
    public bool IncludeMetadata { get; set; } = true;

    /// <summary>
    /// Determines which storage tiers need to be queried based on the date range.
    /// </summary>
    public IEnumerable<StorageTier> GetRequiredTiers()
    {
        var tiers = new List<StorageTier>();
        var now = DateTime.UtcNow;

        // If no date range specified, query all tiers
        if (!StartDate.HasValue && !EndDate.HasValue)
        {
            return new[] { StorageTier.Hot, StorageTier.Warm, StorageTier.Cold };
        }

        var queryStart = StartDate ?? DateTime.MinValue;
        var queryEnd = EndDate ?? now;

        // Check if query overlaps with hot tier (last 90 days)
        var hotTierStart = now.AddDays(-90);
        if (queryEnd >= hotTierStart)
        {
            tiers.Add(StorageTier.Hot);
        }

        // Check if query overlaps with warm tier (90-365 days ago)
        var warmTierStart = now.AddDays(-365);
        var warmTierEnd = now.AddDays(-90);
        if (queryStart <= warmTierEnd && queryEnd >= warmTierStart)
        {
            tiers.Add(StorageTier.Warm);
        }

        // Check if query overlaps with cold tier (365+ days ago)
        var coldTierEnd = now.AddDays(-365);
        if (queryStart <= coldTierEnd)
        {
            tiers.Add(StorageTier.Cold);
        }

        return tiers;
    }

    /// <summary>
    /// Calculates the skip count for pagination.
    /// </summary>
    public int GetSkipCount()
    {
        return (Page - 1) * PageSize;
    }

    /// <summary>
    /// Validates the query parameters.
    /// </summary>
    public bool IsValid()
    {
        if (Page < 1 || PageSize < 1 || PageSize > 1000)
            return false;

        if (StartDate.HasValue && EndDate.HasValue && StartDate > EndDate)
            return false;

        return true;
    }

    /// <summary>
    /// Creates a copy of the query for a specific storage tier.
    /// </summary>
    public HistoryQuery ForTier(StorageTier tier)
    {
        var query = new HistoryQuery
        {
            LeadId = LeadId,
            FieldName = FieldName,
            ChangedBy = ChangedBy,
            StartDate = StartDate,
            EndDate = EndDate,
            Page = Page,
            PageSize = PageSize,
            SortOrder = SortOrder,
            IncludeMetadata = IncludeMetadata
        };

        // Adjust date range based on tier boundaries
        var now = DateTime.UtcNow;
        switch (tier)
        {
            case StorageTier.Hot:
                var hotStart = now.AddDays(-90);
                query.StartDate = query.StartDate.HasValue ? 
                    (query.StartDate > hotStart ? query.StartDate : hotStart) : hotStart;
                break;

            case StorageTier.Warm:
                var warmStart = now.AddDays(-365);
                var warmEnd = now.AddDays(-90);
                query.StartDate = query.StartDate.HasValue ? 
                    (query.StartDate > warmStart ? query.StartDate : warmStart) : warmStart;
                query.EndDate = query.EndDate.HasValue ? 
                    (query.EndDate < warmEnd ? query.EndDate : warmEnd) : warmEnd;
                break;

            case StorageTier.Cold:
                var coldEnd = now.AddDays(-365);
                query.EndDate = query.EndDate.HasValue ? 
                    (query.EndDate < coldEnd ? query.EndDate : coldEnd) : coldEnd;
                break;
        }

        return query;
    }

    public override string ToString()
    {
        var filters = new List<string>();
        
        if (LeadId.HasValue) filters.Add($"LeadId={LeadId}");
        if (!string.IsNullOrEmpty(FieldName)) filters.Add($"FieldName={FieldName}");
        if (!string.IsNullOrEmpty(ChangedBy)) filters.Add($"ChangedBy={ChangedBy}");
        if (StartDate.HasValue) filters.Add($"StartDate={StartDate:yyyy-MM-dd}");
        if (EndDate.HasValue) filters.Add($"EndDate={EndDate:yyyy-MM-dd}");
        
        var filterStr = filters.Any() ? $" WHERE {string.Join(" AND ", filters)}" : "";
        return $"HistoryQuery: Page {Page}, Size {PageSize}{filterStr}";
    }
}

/// <summary>
/// Defines the available sort orders for history queries.
/// </summary>
public enum HistorySortOrder
{
    /// <summary>
    /// Sort by ChangedAt descending (newest first) - default and most common.
    /// </summary>
    ChangedAtDescending = 1,

    /// <summary>
    /// Sort by ChangedAt ascending (oldest first).
    /// </summary>
    ChangedAtAscending = 2,

    /// <summary>
    /// Sort by FieldName ascending, then ChangedAt descending.
    /// </summary>
    FieldNameThenChangedAt = 3,

    /// <summary>
    /// Sort by ChangedBy ascending, then ChangedAt descending.
    /// </summary>
    ChangedByThenChangedAt = 4
}
