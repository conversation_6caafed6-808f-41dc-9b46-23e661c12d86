using Xunit;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Services;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Core.Configuration;
using CrmHistorySystem.Infrastructure.Data;
using CrmHistorySystem.Infrastructure.Services;
using CrmHistorySystem.Infrastructure.Storage;
using CrmHistorySystem.Infrastructure.Caching;

namespace CrmHistorySystem.Tests.Integration;

public class LeadHistoryIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly ILeadHistoryService _leadHistoryService;
    private readonly IHistoryService _historyService;

    public LeadHistoryIntegrationTests()
    {
        var services = new ServiceCollection();
        
        // Configure test services
        services.AddLogging(builder => builder.AddConsole());
        
        // Configure test database
        services.AddDbContext<HistoryDbContext>(options =>
            options.UseInMemoryDatabase($"TestDb_{Guid.NewGuid()}"));

        // Configure test options
        var historyOptions = new HistoryOptions
        {
            HotTierRetentionDays = 90,
            WarmTierRetentionDays = 365,
            BatchSize = 1000,
            CacheExpirationMinutes = 60,
            ConnectionStrings = new ConnectionStrings
            {
                Hot = "InMemory",
                Warm = "InMemory",
                Cold = "InMemory",
                Redis = "localhost:6379"
            }
        };
        services.Configure<HistoryOptions>(opt =>
        {
            opt.HotTierRetentionDays = historyOptions.HotTierRetentionDays;
            opt.WarmTierRetentionDays = historyOptions.WarmTierRetentionDays;
            opt.BatchSize = historyOptions.BatchSize;
            opt.CacheExpirationMinutes = historyOptions.CacheExpirationMinutes;
            opt.ConnectionStrings = historyOptions.ConnectionStrings;
        });

        // Add cache (in-memory for testing)
        services.AddMemoryCache();
        services.AddSingleton<Microsoft.Extensions.Caching.Distributed.IDistributedCache, 
            Microsoft.Extensions.Caching.Memory.MemoryDistributedCache>();
        services.AddSingleton<IHistoryCache, RedisHistoryCache>();

        // Add tier storages (mock implementations for testing)
        services.AddScoped<ITierStorage>(provider => 
            new MockTierStorage(StorageTier.Hot, provider.GetRequiredService<ILogger<MockTierStorage>>()));
        services.AddScoped<ITierStorage>(provider => 
            new MockTierStorage(StorageTier.Warm, provider.GetRequiredService<ILogger<MockTierStorage>>()));
        services.AddScoped<ITierStorage>(provider => 
            new MockTierStorage(StorageTier.Cold, provider.GetRequiredService<ILogger<MockTierStorage>>()));

        // Add main services
        services.AddScoped<IHistoryService, TieredHistoryService>();
        services.AddScoped<ILeadHistoryService, LeadHistoryService>();

        _serviceProvider = services.BuildServiceProvider();
        _leadHistoryService = _serviceProvider.GetRequiredService<ILeadHistoryService>();
        _historyService = _serviceProvider.GetRequiredService<IHistoryService>();
    }

    [Fact]
    public async Task RecordLeadCreationAsync_WithValidLead_RecordsAllNonNullProperties()
    {
        // Arrange
        var lead = new Lead
        {
            Id = 123,
            Name = "John Doe",
            ContactNo = "**********",
            Email = "<EMAIL>",
            Notes = "Test lead",
            AssignTo = Guid.NewGuid(),
            ShareCount = 0
        };

        // Act
        var result = await _leadHistoryService.RecordLeadCreationAsync(lead, "test-user");

        // Assert
        result.Should().BeTrue();

        // Verify history entries were created
        var history = await _leadHistoryService.GetLeadHistoryAsync(123, pageSize: 100);
        history.Data.Should().NotBeEmpty();
        
        // Should have entries for all non-null properties (excluding system properties)
        var expectedFields = new[] { "Name", "ContactNo", "Email", "Notes", "AssignTo", "ShareCount" };
        var actualFields = history.Data.Select(h => h.FieldName).Distinct().ToList();
        
        foreach (var expectedField in expectedFields)
        {
            actualFields.Should().Contain(expectedField, $"History should contain entry for {expectedField}");
        }

        // All entries should have null OldValue (creation)
        history.Data.Should().OnlyContain(h => h.OldValue == null, "Creation entries should have null OldValue");
        
        // All entries should have the correct LeadId and ChangedBy
        history.Data.Should().OnlyContain(h => h.LeadId == 123, "All entries should have correct LeadId");
        history.Data.Should().OnlyContain(h => h.ChangedBy == "test-user", "All entries should have correct ChangedBy");
    }

    [Fact]
    public async Task RecordLeadChangesAsync_WithModifiedLead_RecordsOnlyChangedProperties()
    {
        // Arrange
        var oldLead = new Lead
        {
            Id = 456,
            Name = "Jane Smith",
            ContactNo = "0987654321",
            Email = "<EMAIL>",
            Notes = "Original notes"
        };

        var newLead = new Lead
        {
            Id = 456,
            Name = "Jane Smith-Johnson", // Changed
            ContactNo = "0987654321",     // Unchanged
            Email = "<EMAIL>", // Changed
            Notes = "Original notes"      // Unchanged
        };

        // Act
        var result = await _leadHistoryService.RecordLeadChangesAsync(456, oldLead, newLead, "test-user");

        // Assert
        result.Should().BeTrue();

        // Verify only changed fields were recorded
        var history = await _leadHistoryService.GetLeadHistoryAsync(456, pageSize: 100);
        history.Data.Should().HaveCount(2, "Only 2 fields changed");

        var nameChange = history.Data.FirstOrDefault(h => h.FieldName == "Name");
        nameChange.Should().NotBeNull();
        nameChange!.OldValue.Should().Be("Jane Smith");
        nameChange.NewValue.Should().Be("Jane Smith-Johnson");

        var emailChange = history.Data.FirstOrDefault(h => h.FieldName == "Email");
        emailChange.Should().NotBeNull();
        emailChange!.OldValue.Should().Be("<EMAIL>");
        emailChange.NewValue.Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task RecordFieldChangeAsync_WithValidFieldChange_RecordsSuccessfully()
    {
        // Arrange
        var leadId = 789;
        var fieldName = "Status";
        var oldValue = "New";
        var newValue = "Qualified";
        var changedBy = "test-user";
        var metadata = "{\"reason\": \"Manual status update\"}";

        // Act
        var result = await _leadHistoryService.RecordFieldChangeAsync(
            leadId, fieldName, oldValue, newValue, changedBy, metadata);

        // Assert
        result.Should().BeTrue();

        // Verify the field change was recorded
        var history = await _leadHistoryService.GetLeadHistoryAsync(leadId);
        history.Data.Should().HaveCount(1);

        var entry = history.Data.First();
        entry.LeadId.Should().Be(leadId);
        entry.FieldName.Should().Be(fieldName);
        entry.OldValue.Should().Be(oldValue);
        entry.NewValue.Should().Be(newValue);
        entry.ChangedBy.Should().Be(changedBy);
        entry.Metadata.Should().Be(metadata);
    }

    [Fact]
    public async Task RecordBatchChangesAsync_WithMultipleChanges_RecordsAllChanges()
    {
        // Arrange
        var leadId = 101112;
        var changes = new[]
        {
            FieldChange.ForModification("Name", "Old Name", "New Name"),
            FieldChange.ForModification("Email", "<EMAIL>", "<EMAIL>"),
            FieldChange.ForCreation("Notes", "New notes added")
        };

        // Act
        var result = await _leadHistoryService.RecordBatchChangesAsync(
            leadId, changes, "test-user", "Batch update test");

        // Assert
        result.Should().NotBeNull();
        result.IsSuccess.Should().BeTrue();
        result.SuccessCount.Should().Be(3);
        result.FailureCount.Should().Be(0);

        // Verify all changes were recorded
        var history = await _leadHistoryService.GetLeadHistoryAsync(leadId, pageSize: 100);
        history.Data.Should().HaveCount(3);

        var nameChange = history.Data.FirstOrDefault(h => h.FieldName == "Name");
        nameChange.Should().NotBeNull();
        nameChange!.OldValue.Should().Be("Old Name");
        nameChange.NewValue.Should().Be("New Name");

        var emailChange = history.Data.FirstOrDefault(h => h.FieldName == "Email");
        emailChange.Should().NotBeNull();
        emailChange!.OldValue.Should().Be("<EMAIL>");
        emailChange.NewValue.Should().Be("<EMAIL>");

        var notesChange = history.Data.FirstOrDefault(h => h.FieldName == "Notes");
        notesChange.Should().NotBeNull();
        notesChange!.OldValue.Should().BeNull();
        notesChange.NewValue.Should().Be("New notes added");
    }

    [Fact]
    public async Task GetLeadHistoryAsync_WithFilters_ReturnsFilteredResults()
    {
        // Arrange
        var leadId = 131415;
        
        // Create some test history entries
        await _leadHistoryService.RecordFieldChangeAsync(leadId, "Name", "Old Name", "New Name", "user1");
        await _leadHistoryService.RecordFieldChangeAsync(leadId, "Email", "<EMAIL>", "<EMAIL>", "user2");
        await _leadHistoryService.RecordFieldChangeAsync(leadId, "Name", "New Name", "Final Name", "user1");

        // Act - Filter by field name
        var nameHistory = await _leadHistoryService.GetLeadHistoryAsync(leadId, fieldName: "Name");

        // Assert
        nameHistory.Data.Should().HaveCount(2, "Should return only Name field changes");
        nameHistory.Data.Should().OnlyContain(h => h.FieldName == "Name");

        // Act - Filter by date range
        var recentHistory = await _leadHistoryService.GetLeadHistoryAsync(
            leadId, 
            startDate: DateTime.UtcNow.AddMinutes(-1),
            endDate: DateTime.UtcNow.AddMinutes(1));

        // Assert
        recentHistory.Data.Should().HaveCount(3, "All entries should be within the recent time range");
    }

    [Fact]
    public async Task RecordLeadCreationAsync_WithNullProperties_IgnoresNullValues()
    {
        // Arrange
        var lead = new Lead
        {
            Id = 161718,
            Name = "Test Lead",
            ContactNo = "**********",
            Email = null, // Null property
            Notes = "",   // Empty string
            LandLine = null // Null property
        };

        // Act
        var result = await _leadHistoryService.RecordLeadCreationAsync(lead, "test-user");

        // Assert
        result.Should().BeTrue();

        // Verify only non-null, non-empty properties were recorded
        var history = await _leadHistoryService.GetLeadHistoryAsync(161718, pageSize: 100);
        
        var fieldNames = history.Data.Select(h => h.FieldName).ToList();
        fieldNames.Should().Contain("Name");
        fieldNames.Should().Contain("ContactNo");
        fieldNames.Should().NotContain("Email", "Null properties should not be recorded");
        fieldNames.Should().NotContain("LandLine", "Null properties should not be recorded");
        
        // Empty string should not be recorded
        fieldNames.Should().NotContain("Notes", "Empty string properties should not be recorded");
    }

    [Fact]
    public async Task Integration_CompleteLeadLifecycle_RecordsAllChanges()
    {
        // Arrange
        var leadId = 192021;
        var lead = new Lead
        {
            Id = leadId,
            Name = "Complete Test Lead",
            ContactNo = "5555555555",
            Email = "<EMAIL>"
        };

        // Act 1: Record lead creation
        var creationResult = await _leadHistoryService.RecordLeadCreationAsync(lead, "creator-user");
        creationResult.Should().BeTrue();

        // Act 2: Record some field changes
        await _leadHistoryService.RecordFieldChangeAsync(leadId, "Status", null, "New", "system");
        await _leadHistoryService.RecordFieldChangeAsync(leadId, "Status", "New", "Contacted", "agent1");
        await _leadHistoryService.RecordFieldChangeAsync(leadId, "Status", "Contacted", "Qualified", "agent1");

        // Act 3: Record batch changes
        var batchChanges = new[]
        {
            FieldChange.ForModification("Email", "<EMAIL>", "<EMAIL>"),
            FieldChange.ForCreation("Notes", "Lead is very interested")
        };
        var batchResult = await _leadHistoryService.RecordBatchChangesAsync(
            leadId, batchChanges, "agent2", "Qualification update");
        batchResult.IsSuccess.Should().BeTrue();

        // Assert: Verify complete history
        var completeHistory = await _leadHistoryService.GetLeadHistoryAsync(leadId, pageSize: 100);
        
        // Should have creation entries + individual changes + batch changes
        completeHistory.Data.Should().HaveCountGreaterThan(5);
        
        // Verify we have entries from different users
        var users = completeHistory.Data.Select(h => h.ChangedBy).Distinct().ToList();
        users.Should().Contain("creator-user");
        users.Should().Contain("system");
        users.Should().Contain("agent1");
        users.Should().Contain("agent2");

        // Verify chronological order (most recent first)
        var timestamps = completeHistory.Data.Select(h => h.ChangedAt).ToList();
        timestamps.Should().BeInDescendingOrder("History should be ordered by most recent first");
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}
