# CRM History System - Deployment Guide

This guide provides comprehensive instructions for deploying the CRM History System across different environments.

## 📋 Prerequisites

### Development Environment
- .NET 6 SDK or later
- Docker Desktop
- Azure CLI (for Azure deployments)
- SQL Server Management Studio (optional)

### Production Environment
- Azure subscription with appropriate permissions
- Resource group for the deployment
- SQL Server administrator credentials
- Storage account for cold tier data

## 🚀 Deployment Options

### 1. Local Development with Docker

#### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd CrmHistorySystem

# Start all services
docker-compose up -d

# Check service health
docker-compose ps
```

#### Service URLs
- **API**: http://localhost:8080
- **Swagger UI**: http://localhost:8080
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090

#### Database Connections
- **Hot Tier**: localhost:1433
- **Warm Tier**: localhost:1434
- **Redis**: localhost:6379
- **Azurite**: localhost:10000

### 2. Azure Production Deployment

#### Step 1: Prepare Azure Environment

```bash
# Login to Azure
az login

# Create resource group
az group create \
  --name rg-crm-history-prod \
  --location "East US"

# Set default resource group
az configure --defaults group=rg-crm-history-prod
```

#### Step 2: Deploy Infrastructure

```bash
# Deploy using Bicep template
az deployment group create \
  --resource-group rg-crm-history-prod \
  --template-file azure/main.bicep \
  --parameters \
    environment=prod \
    namePrefix=crm-history \
    sqlAdminLogin=sqladmin \
    sqlAdminPassword='YourSecurePassword123!' \
    appServicePlanSku=P2v3 \
    sqlDatabaseSku=S3
```

#### Step 3: Configure Application

```bash
# Get deployment outputs
WEBAPP_NAME=$(az deployment group show \
  --resource-group rg-crm-history-prod \
  --name main \
  --query properties.outputs.webAppName.value -o tsv)

# Configure application settings
az webapp config appsettings set \
  --name $WEBAPP_NAME \
  --settings \
    "History__HotTierRetentionDays=90" \
    "History__WarmTierRetentionDays=365" \
    "History__BatchSize=1000" \
    "History__Cache__Enabled=true" \
    "History__Performance__Enabled=true"
```

#### Step 4: Deploy Application Code

```bash
# Build and deploy
dotnet publish src/CrmHistorySystem.Api -c Release -o ./publish

# Create deployment package
cd publish
zip -r ../deploy.zip .
cd ..

# Deploy to Azure
az webapp deployment source config-zip \
  --name $WEBAPP_NAME \
  --src deploy.zip
```

#### Step 5: Run Database Migrations

```bash
# Connect to the deployed app and run migrations
az webapp ssh --name $WEBAPP_NAME

# Inside the container
dotnet ef database update --project CrmHistorySystem.Api.dll
```

### 3. Kubernetes Deployment

#### Prerequisites
- Kubernetes cluster (AKS, EKS, or GKE)
- kubectl configured
- Helm 3.x installed

#### Step 1: Create Namespace

```bash
kubectl create namespace crm-history
kubectl config set-context --current --namespace=crm-history
```

#### Step 2: Deploy Dependencies

```bash
# Deploy SQL Server
helm repo add bitnami https://charts.bitnami.com/bitnami
helm install sqlserver-hot bitnami/mssql \
  --set auth.rootPassword=YourSecurePassword123! \
  --set primary.persistence.size=100Gi

helm install sqlserver-warm bitnami/mssql \
  --set auth.rootPassword=YourSecurePassword123! \
  --set primary.persistence.size=200Gi

# Deploy Redis
helm install redis bitnami/redis \
  --set auth.password=YourRedisPassword123!
```

#### Step 3: Deploy Application

```bash
# Apply Kubernetes manifests
kubectl apply -f k8s/
```

#### Step 4: Configure Ingress

```bash
# Install NGINX Ingress Controller
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm install ingress-nginx ingress-nginx/ingress-nginx

# Apply ingress configuration
kubectl apply -f k8s/ingress.yaml
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `ASPNETCORE_ENVIRONMENT` | Environment name | Production | Yes |
| `ConnectionStrings__Hot` | Hot tier SQL connection | - | Yes |
| `ConnectionStrings__Warm` | Warm tier SQL connection | - | Yes |
| `ConnectionStrings__Cold` | Cold tier storage connection | - | Yes |
| `ConnectionStrings__Redis` | Redis connection string | - | Yes |
| `History__HotTierRetentionDays` | Hot tier retention period | 90 | No |
| `History__WarmTierRetentionDays` | Warm tier retention period | 365 | No |
| `History__BatchSize` | Batch processing size | 1000 | No |

### Application Settings

```json
{
  "History": {
    "HotTierRetentionDays": 90,
    "WarmTierRetentionDays": 365,
    "BatchSize": 1000,
    "CacheExpirationMinutes": 60,
    "MaxConcurrentConnections": 10,
    "QueryTimeoutSeconds": 30,
    "MaxRetryAttempts": 3,
    "Cache": {
      "Enabled": true,
      "UseDistributedCache": true,
      "KeyPrefix": "crm_history:"
    },
    "Performance": {
      "Enabled": true,
      "SlowQueryThresholdMs": 1000,
      "LogSlowQueries": true
    },
    "Archival": {
      "AutoArchivalEnabled": true,
      "ArchivalSchedule": "0 2 * * *"
    }
  }
}
```

## 🗄️ Database Setup

### Initial Schema Creation

```sql
-- Run these scripts in order:
-- 1. Create databases
CREATE DATABASE CrmHistory_Hot;
CREATE DATABASE CrmHistory_Warm;

-- 2. Run migration scripts
-- Execute: src/CrmHistorySystem.Infrastructure/Migrations/001_InitialCreate.sql
-- Execute: src/CrmHistorySystem.Infrastructure/Migrations/002_DataMigration.sql
-- Execute: src/CrmHistorySystem.Infrastructure/Migrations/003_RollbackProcedures.sql
```

### Performance Optimization

```sql
-- Hot tier optimizations
ALTER DATABASE CrmHistory_Hot SET AUTO_UPDATE_STATISTICS_ASYNC ON;
ALTER DATABASE CrmHistory_Hot SET PARAMETERIZATION FORCED;

-- Warm tier optimizations
ALTER DATABASE CrmHistory_Warm SET AUTO_UPDATE_STATISTICS_ASYNC ON;
ALTER DATABASE CrmHistory_Warm SET PAGE_VERIFY CHECKSUM;
```

## 📊 Monitoring Setup

### Application Insights (Azure)

```bash
# Enable Application Insights
az monitor app-insights component create \
  --app crm-history-ai-prod \
  --location "East US" \
  --resource-group rg-crm-history-prod \
  --application-type web

# Get instrumentation key
INSTRUMENTATION_KEY=$(az monitor app-insights component show \
  --app crm-history-ai-prod \
  --resource-group rg-crm-history-prod \
  --query instrumentationKey -o tsv)

# Configure application
az webapp config appsettings set \
  --name $WEBAPP_NAME \
  --settings "APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=$INSTRUMENTATION_KEY"
```

### Prometheus & Grafana (Kubernetes)

```bash
# Install Prometheus Operator
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install prometheus prometheus-community/kube-prometheus-stack

# Access Grafana
kubectl port-forward svc/prometheus-grafana 3000:80
```

## 🔒 Security Configuration

### SSL/TLS Setup

```bash
# Generate SSL certificate (Let's Encrypt)
certbot certonly --webroot \
  -w /var/www/html \
  -d api.crmhistory.company.com

# Configure in Azure App Service
az webapp config ssl upload \
  --name $WEBAPP_NAME \
  --certificate-file /path/to/certificate.pfx \
  --certificate-password YourCertPassword
```

### Key Vault Integration

```bash
# Store secrets in Key Vault
az keyvault secret set \
  --vault-name crm-history-kv-prod \
  --name "ConnectionStrings--Hot" \
  --value "Server=...;Database=...;..."

# Configure App Service to use Key Vault
az webapp config appsettings set \
  --name $WEBAPP_NAME \
  --settings "@Microsoft.KeyVault(SecretUri=https://crm-history-kv-prod.vault.azure.net/secrets/ConnectionStrings--Hot/)"
```

## 🧪 Post-Deployment Validation

### Health Checks

```bash
# Check application health
curl -f https://your-app-url/health

# Check database connectivity
curl -f https://your-app-url/health/ready

# Validate API endpoints
curl -X GET "https://your-app-url/api/v1/history/stats"
```

### Performance Testing

```bash
# Install NBomber CLI
dotnet tool install -g NBomber.Cli

# Run performance tests
cd tests/CrmHistorySystem.Tests
dotnet test --filter "Category=Performance"
```

### Data Migration Validation

```sql
-- Validate migration results
EXEC sp_ValidateMigratedData @SourceTable = 'LegacyHistoryTable';

-- Check data distribution
SELECT 
    StorageTier,
    COUNT(*) as EntryCount,
    MIN(ChangedAt) as OldestEntry,
    MAX(ChangedAt) as NewestEntry
FROM vw_HealthEntries_All
GROUP BY StorageTier;
```

## 🔄 Backup and Recovery

### Database Backups

```sql
-- Automated backup configuration
ALTER DATABASE CrmHistory_Hot SET RECOVERY FULL;
ALTER DATABASE CrmHistory_Warm SET RECOVERY FULL;

-- Manual backup
BACKUP DATABASE CrmHistory_Hot 
TO DISK = 'C:\Backups\CrmHistory_Hot.bak'
WITH COMPRESSION, CHECKSUM;
```

### Disaster Recovery

```bash
# Azure SQL Database Point-in-Time Restore
az sql db restore \
  --dest-name CrmHistory_Hot_Restored \
  --server crm-history-sql-prod \
  --source-database CrmHistory_Hot \
  --time "2023-12-01T10:00:00Z"
```

## 📈 Scaling Considerations

### Horizontal Scaling

```bash
# Scale out App Service
az appservice plan update \
  --name crm-history-asp-prod \
  --number-of-workers 3

# Scale Kubernetes deployment
kubectl scale deployment crm-history-api --replicas=5
```

### Database Scaling

```bash
# Scale up SQL Database
az sql db update \
  --name CrmHistory_Hot \
  --server crm-history-sql-prod \
  --service-objective S4
```

## 🚨 Troubleshooting

### Common Issues

1. **Connection Timeouts**
   - Check firewall rules
   - Verify connection strings
   - Monitor connection pool usage

2. **Performance Issues**
   - Check query execution plans
   - Monitor cache hit rates
   - Review index usage

3. **Memory Issues**
   - Monitor application memory usage
   - Check for memory leaks
   - Adjust batch sizes

### Diagnostic Commands

```bash
# Check application logs
az webapp log tail --name $WEBAPP_NAME

# Monitor performance counters
az monitor metrics list \
  --resource $WEBAPP_NAME \
  --metric "CpuPercentage,MemoryPercentage"

# Database diagnostics
az sql db show-usage \
  --name CrmHistory_Hot \
  --server crm-history-sql-prod
```

## 📞 Support

For deployment issues:
1. Check the troubleshooting section above
2. Review application logs
3. Contact the development team
4. Create a support ticket with detailed error information

## 🔄 Rollback Procedures

### Application Rollback

```bash
# Rollback to previous deployment slot
az webapp deployment slot swap \
  --name $WEBAPP_NAME \
  --slot staging \
  --target-slot production

# Rollback using deployment history
az webapp deployment source show \
  --name $WEBAPP_NAME

az webapp deployment source config \
  --name $WEBAPP_NAME \
  --repo-url <previous-commit-url>
```

### Database Rollback

```sql
-- Use the rollback procedures created during migration
EXEC sp_RollbackMigration 
  @BackupName = 'PreMigration_20231201_120000',
  @ConfirmRollback = 'YES';
```
