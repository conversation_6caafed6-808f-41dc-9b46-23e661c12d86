using System.ComponentModel.DataAnnotations;
using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Api.Models;

/// <summary>
/// Standard API response wrapper.
/// </summary>
/// <typeparam name="T">Type of data being returned</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// Indicates if the operation was successful.
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// The response data.
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// Error message if the operation failed.
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Detailed error information.
    /// </summary>
    public string? ErrorDetails { get; set; }

    /// <summary>
    /// Timestamp of the response.
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Creates a successful response.
    /// </summary>
    public static ApiResponse<T> Success(T data)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Data = data
        };
    }

    /// <summary>
    /// Creates an error response.
    /// </summary>
    public static ApiResponse<T> Error(string message, string? details = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            ErrorMessage = message,
            ErrorDetails = details
        };
    }
}

/// <summary>
/// Request model for querying history entries.
/// </summary>
public class HistoryQueryRequest
{
    /// <summary>
    /// Filter by specific lead ID.
    /// </summary>
    public int? LeadId { get; set; }

    /// <summary>
    /// Filter by specific field name.
    /// </summary>
    [StringLength(100)]
    public string? FieldName { get; set; }

    /// <summary>
    /// Filter by user who made the change.
    /// </summary>
    [StringLength(100)]
    public string? ChangedBy { get; set; }

    /// <summary>
    /// Start date for the query range (inclusive).
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// End date for the query range (inclusive).
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Page number for pagination (1-based).
    /// </summary>
    [Range(1, int.MaxValue)]
    public int Page { get; set; } = 1;

    /// <summary>
    /// Number of records per page.
    /// </summary>
    [Range(1, 1000)]
    public int PageSize { get; set; } = 50;

    /// <summary>
    /// Sort order for results.
    /// </summary>
    public HistorySortOrder SortOrder { get; set; } = HistorySortOrder.ChangedAtDescending;

    /// <summary>
    /// Whether to include metadata in the results.
    /// </summary>
    public bool IncludeMetadata { get; set; } = true;

    /// <summary>
    /// Converts to core HistoryQuery model.
    /// </summary>
    public HistoryQuery ToHistoryQuery()
    {
        return new HistoryQuery
        {
            LeadId = LeadId,
            FieldName = FieldName,
            ChangedBy = ChangedBy,
            StartDate = StartDate,
            EndDate = EndDate,
            Page = Page,
            PageSize = PageSize,
            SortOrder = SortOrder,
            IncludeMetadata = IncludeMetadata
        };
    }
}

/// <summary>
/// Request model for creating a history entry.
/// </summary>
public class CreateHistoryEntryRequest
{
    /// <summary>
    /// The ID of the lead that was modified.
    /// </summary>
    [Required]
    [Range(1, int.MaxValue)]
    public int LeadId { get; set; }

    /// <summary>
    /// The name of the field that was changed.
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 1)]
    public string FieldName { get; set; } = string.Empty;

    /// <summary>
    /// The previous value of the field before the change.
    /// </summary>
    [StringLength(4000)]
    public string? OldValue { get; set; }

    /// <summary>
    /// The new value of the field after the change.
    /// </summary>
    [StringLength(4000)]
    public string? NewValue { get; set; }

    /// <summary>
    /// Timestamp when the change occurred.
    /// </summary>
    public DateTime? ChangedAt { get; set; }

    /// <summary>
    /// Identifier of the user who made the change.
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 1)]
    public string ChangedBy { get; set; } = string.Empty;

    /// <summary>
    /// Optional metadata for additional context about the change.
    /// </summary>
    [StringLength(2000)]
    public string? Metadata { get; set; }

    /// <summary>
    /// Converts to core HistoryEntry model.
    /// </summary>
    public HistoryEntry ToHistoryEntry()
    {
        return new HistoryEntry
        {
            LeadId = LeadId,
            FieldName = FieldName,
            OldValue = OldValue,
            NewValue = NewValue,
            ChangedAt = ChangedAt ?? DateTime.UtcNow,
            ChangedBy = ChangedBy,
            Metadata = Metadata
        };
    }
}

/// <summary>
/// Request model for creating a batch of history entries.
/// </summary>
public class CreateHistoryBatchRequest
{
    /// <summary>
    /// The history entries in this batch.
    /// </summary>
    [Required]
    [MinLength(1)]
    [MaxLength(1000)]
    public List<CreateHistoryEntryRequest> Entries { get; set; } = new();

    /// <summary>
    /// User or system that created this batch.
    /// </summary>
    [Required]
    [StringLength(100, MinimumLength = 1)]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// Optional metadata for the batch operation.
    /// </summary>
    [StringLength(2000)]
    public string? Metadata { get; set; }

    /// <summary>
    /// Converts to core HistoryBatch model.
    /// </summary>
    public HistoryBatch ToHistoryBatch()
    {
        var batch = new HistoryBatch
        {
            CreatedBy = CreatedBy,
            Metadata = Metadata
        };

        foreach (var entryRequest in Entries)
        {
            batch.TryAddEntry(entryRequest.ToHistoryEntry());
        }

        return batch;
    }
}

/// <summary>
/// Request model for archiving old entries.
/// </summary>
public class ArchiveRequest
{
    /// <summary>
    /// Entries older than this date will be archived.
    /// </summary>
    [Required]
    public DateTime CutoffDate { get; set; }
}

/// <summary>
/// Request model for clearing cache.
/// </summary>
public class ClearCacheRequest
{
    /// <summary>
    /// Optional lead ID to clear cache for.
    /// </summary>
    public int? LeadId { get; set; }

    /// <summary>
    /// Optional field name to clear cache for.
    /// </summary>
    [StringLength(100)]
    public string? FieldName { get; set; }
}

/// <summary>
/// DTO for history entry.
/// </summary>
public class HistoryEntryDto
{
    public long Id { get; set; }
    public int LeadId { get; set; }
    public string FieldName { get; set; } = string.Empty;
    public string? OldValue { get; set; }
    public string? NewValue { get; set; }
    public DateTime ChangedAt { get; set; }
    public string ChangedBy { get; set; } = string.Empty;
    public string? Metadata { get; set; }
    public string StorageTier { get; set; } = string.Empty;

    public static HistoryEntryDto FromHistoryEntry(HistoryEntry entry)
    {
        return new HistoryEntryDto
        {
            Id = entry.Id,
            LeadId = entry.LeadId,
            FieldName = entry.FieldName,
            OldValue = entry.OldValue,
            NewValue = entry.NewValue,
            ChangedAt = entry.ChangedAt,
            ChangedBy = entry.ChangedBy,
            Metadata = entry.Metadata,
            StorageTier = entry.GetStorageTier().ToString()
        };
    }
}

/// <summary>
/// DTO for batch operation result.
/// </summary>
public class BatchOperationResultDto
{
    public Guid BatchId { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public int TotalCount { get; set; }
    public long ProcessingTimeMs { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public bool IsSuccess { get; set; }
    public Dictionary<string, int> TierDistribution { get; set; } = new();

    public static BatchOperationResultDto FromBatchOperationResult(BatchOperationResult result)
    {
        return new BatchOperationResultDto
        {
            BatchId = result.BatchId,
            SuccessCount = result.SuccessCount,
            FailureCount = result.FailureCount,
            TotalCount = result.TotalCount,
            ProcessingTimeMs = result.ProcessingTimeMs,
            Errors = result.Errors,
            Warnings = result.Warnings,
            IsSuccess = result.IsSuccess,
            TierDistribution = result.TierDistribution.ToDictionary(
                kvp => kvp.Key.ToString(), 
                kvp => kvp.Value)
        };
    }
}

/// <summary>
/// DTO for storage statistics.
/// </summary>
public class HistoryStorageStatsDto
{
    public Dictionary<string, TierStorageStatsDto> TierStats { get; set; } = new();
    public long TotalEntries { get; set; }
    public long TotalSizeBytes { get; set; }
    public decimal EstimatedMonthlyCost { get; set; }
    public DateTime CollectedAt { get; set; }
    public string DistributionSummary { get; set; } = string.Empty;

    public static HistoryStorageStatsDto FromHistoryStorageStats(HistoryStorageStats stats)
    {
        return new HistoryStorageStatsDto
        {
            TierStats = stats.TierStats.ToDictionary(
                kvp => kvp.Key.ToString(),
                kvp => TierStorageStatsDto.FromTierStorageStats(kvp.Value)),
            TotalEntries = stats.TotalEntries,
            TotalSizeBytes = stats.TotalSizeBytes,
            EstimatedMonthlyCost = stats.EstimatedMonthlyCost,
            CollectedAt = stats.CollectedAt,
            DistributionSummary = stats.GetDistributionSummary()
        };
    }
}

/// <summary>
/// DTO for tier storage statistics.
/// </summary>
public class TierStorageStatsDto
{
    public string Tier { get; set; } = string.Empty;
    public long EntryCount { get; set; }
    public long SizeBytes { get; set; }
    public string FormattedSize { get; set; } = string.Empty;
    public double AverageQueryTimeMs { get; set; }
    public decimal EstimatedMonthlyCost { get; set; }
    public DateTime? OldestEntry { get; set; }
    public DateTime? NewestEntry { get; set; }
    public long QueriesLast24Hours { get; set; }
    public double CacheHitRate { get; set; }

    public static TierStorageStatsDto FromTierStorageStats(TierStorageStats stats)
    {
        return new TierStorageStatsDto
        {
            Tier = stats.Tier.ToString(),
            EntryCount = stats.EntryCount,
            SizeBytes = stats.SizeBytes,
            FormattedSize = stats.GetFormattedSize(),
            AverageQueryTimeMs = stats.AverageQueryTimeMs,
            EstimatedMonthlyCost = stats.EstimatedMonthlyCost,
            OldestEntry = stats.OldestEntry,
            NewestEntry = stats.NewestEntry,
            QueriesLast24Hours = stats.QueriesLast24Hours,
            CacheHitRate = stats.CacheHitRate
        };
    }
}

/// <summary>
/// DTO for data integrity result.
/// </summary>
public class DataIntegrityResultDto
{
    public bool IsValid { get; set; }
    public List<IntegrityIssueDto> Issues { get; set; } = new();
    public Dictionary<string, long> RecordsValidated { get; set; } = new();
    public long ValidationTimeMs { get; set; }
    public DateTime ValidatedAt { get; set; }
    public string IssueSummary { get; set; } = string.Empty;

    public static DataIntegrityResultDto FromDataIntegrityResult(DataIntegrityResult result)
    {
        return new DataIntegrityResultDto
        {
            IsValid = result.IsValid,
            Issues = result.Issues.Select(IntegrityIssueDto.FromIntegrityIssue).ToList(),
            RecordsValidated = result.RecordsValidated.ToDictionary(
                kvp => kvp.Key.ToString(),
                kvp => kvp.Value),
            ValidationTimeMs = result.ValidationTimeMs,
            ValidatedAt = result.ValidatedAt,
            IssueSummary = result.GetIssueSummary()
        };
    }
}

/// <summary>
/// DTO for integrity issue.
/// </summary>
public class IntegrityIssueDto
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? Tier { get; set; }
    public DateTime DetectedAt { get; set; }
    public Dictionary<string, object> Context { get; set; } = new();

    public static IntegrityIssueDto FromIntegrityIssue(IntegrityIssue issue)
    {
        return new IntegrityIssueDto
        {
            Type = issue.Type.ToString(),
            Description = issue.Description,
            Tier = issue.Tier?.ToString(),
            DetectedAt = issue.DetectedAt,
            Context = issue.Context
        };
    }
}

/// <summary>
/// DTO for archival result.
/// </summary>
public class ArchivalResultDto
{
    public DateTime CutoffDate { get; set; }
    public int ArchivedCount { get; set; }
    public DateTime CompletedAt { get; set; }
}
