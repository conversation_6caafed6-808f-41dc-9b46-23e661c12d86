<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM History System Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h2 {
            color: #34495e;
            margin-top: 0;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .history-entry {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #3498db;
            border-radius: 4px;
        }
        .history-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .change-detail {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #3498db;
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 CRM History System Demo</h1>
        <p style="text-align: center; color: #666;">
            Demonstrating automatic lead change tracking with Supabase PostgreSQL backend
        </p>

        <!-- Connection Status -->
        <div class="section">
            <h2>📡 Connection Status</h2>
            <div id="connectionStatus">Testing connection...</div>
            <button onclick="testConnection()">Test Connection</button>
        </div>

        <!-- Create Lead Form -->
        <div class="section">
            <h2>📝 Create New Lead</h2>
            <div class="form-group">
                <label for="leadName">Name:</label>
                <input type="text" id="leadName" placeholder="Enter lead name">
            </div>
            <div class="form-group">
                <label for="leadContact">Contact Number:</label>
                <input type="text" id="leadContact" placeholder="Enter contact number">
            </div>
            <div class="form-group">
                <label for="leadEmail">Email:</label>
                <input type="email" id="leadEmail" placeholder="Enter email address">
            </div>
            <div class="form-group">
                <label for="leadNotes">Notes:</label>
                <textarea id="leadNotes" rows="3" placeholder="Enter notes about the lead"></textarea>
            </div>
            <div class="form-group">
                <label for="leadProject">Chosen Project:</label>
                <select id="leadProject">
                    <option value="">Select a project</option>
                    <option value="Luxury Apartments">Luxury Apartments</option>
                    <option value="Premium Villas">Premium Villas</option>
                    <option value="Commercial Spaces">Commercial Spaces</option>
                    <option value="Budget Homes">Budget Homes</option>
                </select>
            </div>
            <button onclick="createLead()">Create Lead</button>
            <div id="createResult"></div>
        </div>

        <!-- Leads List -->
        <div class="section">
            <h2>📋 Existing Leads</h2>
            <button onclick="loadLeads()">Refresh Leads</button>
            <div id="leadsList"></div>
        </div>

        <!-- History Viewer -->
        <div class="section">
            <h2>📊 Lead History</h2>
            <div class="form-group">
                <label for="historyLeadId">Select Lead:</label>
                <select id="historyLeadId">
                    <option value="">Select a lead to view history</option>
                </select>
            </div>
            <button onclick="loadHistory()">Load History</button>
            <div id="historyList"></div>
        </div>

        <!-- Statistics -->
        <div class="section">
            <h2>📈 System Statistics</h2>
            <div class="stats" id="statsContainer">
                <div class="stat-card">
                    <div class="stat-number" id="totalLeads">-</div>
                    <div class="stat-label">Total Leads</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalHistory">-</div>
                    <div class="stat-label">History Entries</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="avgChanges">-</div>
                    <div class="stat-label">Avg Changes/Lead</div>
                </div>
            </div>
            <button onclick="loadStats()">Refresh Statistics</button>
        </div>
    </div>

    <script>
        const SUPABASE_URL = 'https://ezcwxlffzmqwdzpqxtan.supabase.co';
        const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV6Y3d4bGZmem1xd2R6cHF4dGFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1NzE5NTcsImV4cCI6MjA2ODE0Nzk1N30.1Zy3WPXbCRZs-piu5tW845EOQDSgxiJt8dizsn4Wi5M';

        const headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': `Bearer ${SUPABASE_KEY}`,
            'Content-Type': 'application/json',
            'Prefer': 'return=representation'
        };

        async function testConnection() {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.innerHTML = 'Testing connection...';
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/`, { headers });
                if (response.ok) {
                    statusDiv.innerHTML = '<div class="success">✅ Connected to Supabase successfully!</div>';
                    await initializeTables();
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ Connection failed</div>';
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ Connection error: ${error.message}</div>`;
            }
        }

        async function initializeTables() {
            try {
                // Check if tables exist by trying to query them
                await fetch(`${SUPABASE_URL}/rest/v1/leads?limit=1`, { headers });
                console.log('Tables already exist');
            } catch (error) {
                console.log('Tables may need to be created');
            }
        }

        async function createLead() {
            const name = document.getElementById('leadName').value;
            const contact = document.getElementById('leadContact').value;
            const email = document.getElementById('leadEmail').value;
            const notes = document.getElementById('leadNotes').value;
            const project = document.getElementById('leadProject').value;
            const resultDiv = document.getElementById('createResult');

            if (!name || !contact) {
                resultDiv.innerHTML = '<div class="error">Name and Contact Number are required</div>';
                return;
            }

            try {
                // Create lead
                const leadData = {
                    name: name,
                    contact_no: contact,
                    email: email || null,
                    notes: notes || null,
                    chosen_project: project || null,
                    assign_to: crypto.randomUUID(),
                    created_by: 'demo-user',
                    updated_by: 'demo-user',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };

                const response = await fetch(`${SUPABASE_URL}/rest/v1/leads`, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(leadData)
                });

                if (response.ok) {
                    const createdLead = await response.json();
                    const leadId = createdLead[0].id;

                    // Create history entries
                    const historyEntries = [];
                    Object.entries(leadData).forEach(([key, value]) => {
                        if (value && !['created_at', 'updated_at', 'assign_to'].includes(key)) {
                            historyEntries.push({
                                lead_id: leadId,
                                field_name: key,
                                old_value: null,
                                new_value: String(value),
                                changed_by: 'demo-user',
                                changed_at: new Date().toISOString(),
                                tier: 'Hot'
                            });
                        }
                    });

                    if (historyEntries.length > 0) {
                        await fetch(`${SUPABASE_URL}/rest/v1/history_entries`, {
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify(historyEntries)
                        });
                    }

                    resultDiv.innerHTML = `<div class="success">✅ Lead created successfully! ID: ${leadId}</div>`;
                    
                    // Clear form
                    document.getElementById('leadName').value = '';
                    document.getElementById('leadContact').value = '';
                    document.getElementById('leadEmail').value = '';
                    document.getElementById('leadNotes').value = '';
                    document.getElementById('leadProject').value = '';
                    
                    // Refresh leads list
                    loadLeads();
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to create lead</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }

        async function loadLeads() {
            const listDiv = document.getElementById('leadsList');
            const selectDiv = document.getElementById('historyLeadId');
            
            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/leads?order=created_at.desc`, { headers });
                if (response.ok) {
                    const leads = await response.json();
                    
                    listDiv.innerHTML = leads.map(lead => `
                        <div class="history-entry">
                            <strong>${lead.name}</strong> (${lead.contact_no})
                            <br>Email: ${lead.email || 'N/A'}
                            <br>Project: ${lead.chosen_project || 'N/A'}
                            <br>Created: ${new Date(lead.created_at).toLocaleString()}
                        </div>
                    `).join('');

                    // Update history select dropdown
                    selectDiv.innerHTML = '<option value="">Select a lead to view history</option>' +
                        leads.map(lead => `<option value="${lead.id}">${lead.name} (${lead.contact_no})</option>`).join('');
                } else {
                    listDiv.innerHTML = '<div class="error">Failed to load leads</div>';
                }
            } catch (error) {
                listDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        async function loadHistory() {
            const leadId = document.getElementById('historyLeadId').value;
            const historyDiv = document.getElementById('historyList');
            
            if (!leadId) {
                historyDiv.innerHTML = '<div class="error">Please select a lead first</div>';
                return;
            }

            try {
                const response = await fetch(`${SUPABASE_URL}/rest/v1/history_entries?lead_id=eq.${leadId}&order=changed_at.desc`, { headers });
                if (response.ok) {
                    const history = await response.json();
                    
                    if (history.length === 0) {
                        historyDiv.innerHTML = '<div>No history found for this lead</div>';
                        return;
                    }

                    historyDiv.innerHTML = history.map(entry => `
                        <div class="history-entry">
                            <div class="history-meta">
                                ${new Date(entry.changed_at).toLocaleString()} by ${entry.changed_by} (${entry.tier} tier)
                            </div>
                            <div class="change-detail">
                                <strong>${entry.field_name}:</strong> 
                                ${entry.old_value ? `"${entry.old_value}" → ` : ''}
                                "${entry.new_value}"
                            </div>
                        </div>
                    `).join('');
                } else {
                    historyDiv.innerHTML = '<div class="error">Failed to load history</div>';
                }
            } catch (error) {
                historyDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        async function loadStats() {
            try {
                const [leadsResponse, historyResponse] = await Promise.all([
                    fetch(`${SUPABASE_URL}/rest/v1/leads?select=count`, { headers: { ...headers, 'Prefer': 'count=exact' } }),
                    fetch(`${SUPABASE_URL}/rest/v1/history_entries?select=count`, { headers: { ...headers, 'Prefer': 'count=exact' } })
                ]);

                const leadsCount = parseInt(leadsResponse.headers.get('content-range')?.split('/')[1] || '0');
                const historyCount = parseInt(historyResponse.headers.get('content-range')?.split('/')[1] || '0');
                const avgChanges = leadsCount > 0 ? (historyCount / leadsCount).toFixed(1) : '0';

                document.getElementById('totalLeads').textContent = leadsCount;
                document.getElementById('totalHistory').textContent = historyCount;
                document.getElementById('avgChanges').textContent = avgChanges;
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Initialize the demo
        window.onload = function() {
            testConnection();
            loadLeads();
            loadStats();
        };
    </script>
</body>
</html>
