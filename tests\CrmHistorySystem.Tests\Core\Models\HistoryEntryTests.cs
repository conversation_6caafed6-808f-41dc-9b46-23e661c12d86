using Xunit;
using FluentAssertions;
using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Tests.Core.Models;

public class HistoryEntryTests
{
    [Fact]
    public void GetStorageTier_RecentEntry_ReturnsHotTier()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            LeadId = 1,
            FieldName = "TestField",
            ChangedAt = DateTime.UtcNow.AddDays(-30),
            ChangedBy = "TestUser"
        };

        // Act
        var tier = entry.GetStorageTier();

        // Assert
        tier.Should().Be(StorageTier.Hot);
    }

    [Fact]
    public void GetStorageTier_MediumAgeEntry_ReturnsWarmTier()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            LeadId = 1,
            FieldName = "TestField",
            ChangedAt = DateTime.UtcNow.AddDays(-180),
            ChangedBy = "TestUser"
        };

        // Act
        var tier = entry.GetStorageTier();

        // Assert
        tier.Should().Be(StorageTier.Warm);
    }

    [Fact]
    public void GetStorageTier_OldEntry_ReturnsColdTier()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            LeadId = 1,
            FieldName = "TestField",
            ChangedAt = DateTime.UtcNow.AddDays(-400),
            ChangedBy = "TestUser"
        };

        // Act
        var tier = entry.GetStorageTier();

        // Assert
        tier.Should().Be(StorageTier.Cold);
    }

    [Fact]
    public void IsValid_ValidEntry_ReturnsTrue()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            LeadId = 1,
            FieldName = "TestField",
            OldValue = "OldValue",
            NewValue = "NewValue",
            ChangedAt = DateTime.UtcNow,
            ChangedBy = "TestUser"
        };

        // Act
        var isValid = entry.IsValid();

        // Assert
        isValid.Should().BeTrue();
    }

    [Theory]
    [InlineData(0, "TestField", "TestUser", false)] // Invalid LeadId
    [InlineData(1, "", "TestUser", false)] // Empty FieldName
    [InlineData(1, "TestField", "", false)] // Empty ChangedBy
    [InlineData(1, "TestField", "TestUser", true)] // Valid entry
    public void IsValid_VariousInputs_ReturnsExpectedResult(int leadId, string fieldName, string changedBy, bool expected)
    {
        // Arrange
        var entry = new HistoryEntry
        {
            LeadId = leadId,
            FieldName = fieldName,
            OldValue = "OldValue",
            NewValue = "NewValue",
            ChangedAt = DateTime.UtcNow,
            ChangedBy = changedBy
        };

        // Act
        var isValid = entry.IsValid();

        // Assert
        isValid.Should().Be(expected);
    }

    [Fact]
    public void Clone_ValidEntry_ReturnsExactCopy()
    {
        // Arrange
        var original = new HistoryEntry
        {
            Id = 123,
            LeadId = 1,
            FieldName = "TestField",
            OldValue = "OldValue",
            NewValue = "NewValue",
            ChangedAt = DateTime.UtcNow,
            ChangedBy = "TestUser",
            Metadata = "TestMetadata"
        };

        // Act
        var clone = original.Clone();

        // Assert
        clone.Should().NotBeSameAs(original);
        clone.Id.Should().Be(original.Id);
        clone.LeadId.Should().Be(original.LeadId);
        clone.FieldName.Should().Be(original.FieldName);
        clone.OldValue.Should().Be(original.OldValue);
        clone.NewValue.Should().Be(original.NewValue);
        clone.ChangedAt.Should().Be(original.ChangedAt);
        clone.ChangedBy.Should().Be(original.ChangedBy);
        clone.Metadata.Should().Be(original.Metadata);
    }

    [Fact]
    public void ToString_ValidEntry_ReturnsFormattedString()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            Id = 123,
            LeadId = 1,
            FieldName = "TestField",
            ChangedBy = "TestUser",
            ChangedAt = new DateTime(2023, 12, 1, 10, 30, 0)
        };

        // Act
        var result = entry.ToString();

        // Assert
        result.Should().Contain("HistoryEntry[123]");
        result.Should().Contain("Lead 1");
        result.Should().Contain("Field 'TestField'");
        result.Should().Contain("Changed by TestUser");
        result.Should().Contain("2023-12-01 10:30:00");
    }
}
