using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Core.Configuration;
using CrmHistorySystem.Infrastructure.Data;
using System.Diagnostics;

namespace CrmHistorySystem.Infrastructure.Storage;

/// <summary>
/// SQL Server implementation for hot and warm tier storage.
/// Optimized for high-performance queries with proper indexing and batching.
/// </summary>
public class SqlServerTierStorage : ITierStorage
{
    private readonly HistoryDbContext _context;
    private readonly ILogger<SqlServerTierStorage> _logger;
    private readonly HistoryOptions _options;

    public StorageTier Tier { get; }

    public SqlServerTierStorage(
        HistoryDbContext context,
        ILogger<SqlServerTierStorage> logger,
        IOptions<HistoryOptions> options)
    {
        _context = context;
        _logger = logger;
        _options = options.Value;
        Tier = context.Tier;
    }

    /// <summary>
    /// Queries history entries from this specific tier.
    /// </summary>
    public async Task<HistoryResult<HistoryEntry>> QueryAsync(
        HistoryQuery query, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            var queryable = BuildQueryable(query);
            
            // Get total count for pagination
            var totalCount = await queryable.CountAsync(cancellationToken);
            
            // Apply pagination and sorting
            var data = await queryable
                .Skip(query.GetSkipCount())
                .Take(query.PageSize)
                .ToListAsync(cancellationToken);

            stopwatch.Stop();
            
            var result = HistoryResult<HistoryEntry>.Success(
                data, 
                totalCount, 
                query.Page, 
                query.PageSize, 
                stopwatch.ElapsedMilliseconds);

            result.QueriedTiers = new[] { Tier };

            _logger.LogDebug("Query completed for tier {Tier}: {Count} results in {ElapsedMs}ms", 
                Tier, data.Count, stopwatch.ElapsedMilliseconds);

            // Log slow queries for performance monitoring
            if (stopwatch.ElapsedMilliseconds > _options.Performance.SlowQueryThresholdMs)
            {
                _logger.LogWarning("Slow query detected in tier {Tier}: {ElapsedMs}ms, Query: {Query}", 
                    Tier, stopwatch.ElapsedMilliseconds, query);
            }

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error querying tier {Tier}: {Query}", Tier, query);
            throw;
        }
    }

    /// <summary>
    /// Adds history entries to this specific tier.
    /// </summary>
    public async Task<bool> AddEntriesAsync(
        IEnumerable<HistoryEntry> entries, 
        CancellationToken cancellationToken = default)
    {
        var entryList = entries.ToList();
        if (!entryList.Any())
            return true;

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Filter entries that belong to this tier
            var tierEntries = entryList.Where(e => e.GetStorageTier() == Tier).ToList();
            
            if (!tierEntries.Any())
            {
                _logger.LogDebug("No entries for tier {Tier} in batch of {Count}", Tier, entryList.Count);
                return true;
            }

            // Process in batches for optimal performance
            var batchSize = _options.BatchSize;
            var totalProcessed = 0;

            for (int i = 0; i < tierEntries.Count; i += batchSize)
            {
                var batch = tierEntries.Skip(i).Take(batchSize);
                var processed = await _context.BulkInsertHistoryEntriesAsync(batch, cancellationToken);
                totalProcessed += processed;
            }

            stopwatch.Stop();
            
            _logger.LogDebug("Added {Count} entries to tier {Tier} in {ElapsedMs}ms", 
                totalProcessed, Tier, stopwatch.ElapsedMilliseconds);

            return totalProcessed == tierEntries.Count;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error adding {Count} entries to tier {Tier}", entryList.Count, Tier);
            throw;
        }
    }

    /// <summary>
    /// Moves entries from this tier to a different tier.
    /// </summary>
    public async Task<int> MoveEntriesAsync(
        DateTime cutoffDate, 
        StorageTier targetTier, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Find entries to move
            var entriesToMove = await _context.HistoryEntries
                .Where(e => e.ChangedAt < cutoffDate)
                .OrderBy(e => e.ChangedAt)
                .Take(_options.Archival.ArchivalBatchSize)
                .ToListAsync(cancellationToken);

            if (!entriesToMove.Any())
            {
                _logger.LogDebug("No entries to move from tier {Tier} before {CutoffDate}", Tier, cutoffDate);
                return 0;
            }

            // This would typically involve:
            // 1. Insert into target tier storage
            // 2. Delete from current tier
            // For now, we'll just log the operation
            
            _logger.LogInformation("Would move {Count} entries from tier {Tier} to {TargetTier}", 
                entriesToMove.Count, Tier, targetTier);

            stopwatch.Stop();
            return entriesToMove.Count;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error moving entries from tier {Tier} to {TargetTier}", Tier, targetTier);
            throw;
        }
    }

    /// <summary>
    /// Gets storage statistics for this tier.
    /// </summary>
    public async Task<TierStorageStats> GetStatsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.GetStorageStatsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting storage stats for tier {Tier}", Tier);
            throw;
        }
    }

    private IQueryable<HistoryEntry> BuildQueryable(HistoryQuery query)
    {
        var queryable = _context.HistoryEntries.AsQueryable();

        // Apply filters
        if (query.LeadId.HasValue)
        {
            queryable = queryable.Where(e => e.LeadId == query.LeadId.Value);
        }

        if (!string.IsNullOrEmpty(query.FieldName))
        {
            queryable = queryable.Where(e => e.FieldName == query.FieldName);
        }

        if (!string.IsNullOrEmpty(query.ChangedBy))
        {
            queryable = queryable.Where(e => e.ChangedBy == query.ChangedBy);
        }

        if (query.StartDate.HasValue)
        {
            queryable = queryable.Where(e => e.ChangedAt >= query.StartDate.Value);
        }

        if (query.EndDate.HasValue)
        {
            queryable = queryable.Where(e => e.ChangedAt <= query.EndDate.Value);
        }

        // Apply sorting
        queryable = query.SortOrder switch
        {
            HistorySortOrder.ChangedAtAscending => queryable.OrderBy(e => e.ChangedAt),
            HistorySortOrder.FieldNameThenChangedAt => queryable.OrderBy(e => e.FieldName).ThenByDescending(e => e.ChangedAt),
            HistorySortOrder.ChangedByThenChangedAt => queryable.OrderBy(e => e.ChangedBy).ThenByDescending(e => e.ChangedAt),
            _ => queryable.OrderByDescending(e => e.ChangedAt) // Default: ChangedAtDescending
        };

        // Exclude metadata if not requested for better performance
        if (!query.IncludeMetadata)
        {
            queryable = queryable.Select(e => new HistoryEntry
            {
                Id = e.Id,
                LeadId = e.LeadId,
                FieldName = e.FieldName,
                OldValue = e.OldValue,
                NewValue = e.NewValue,
                ChangedAt = e.ChangedAt,
                ChangedBy = e.ChangedBy
                // Metadata is excluded
            });
        }

        return queryable;
    }
}
