using Microsoft.AspNetCore.Mvc;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Core.Services;
using CrmHistorySystem.Api.Models;
using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Api.Controllers;

/// <summary>
/// API controller for lead management with integrated history tracking.
/// Demonstrates integration between lead operations and the tiered history system.
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Produces("application/json")]
public class LeadsController : ControllerBase
{
    private readonly ILeadHistoryService _leadHistoryService;
    private readonly ILogger<LeadsController> _logger;

    public LeadsController(
        ILeadHistoryService leadHistoryService,
        ILogger<LeadsController> logger)
    {
        _leadHistoryService = leadHistoryService;
        _logger = logger;
    }

    /// <summary>
    /// Creates a new lead with automatic history tracking.
    /// </summary>
    /// <param name="request">Lead creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created lead with history tracking confirmation</returns>
    /// <response code="201">Lead created successfully</response>
    /// <response code="400">Invalid lead data</response>
    /// <response code="500">Internal server error</response>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<CreateLeadResponse>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<CreateLeadResponse>>> CreateLead(
        [FromBody] CreateLeadRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the request
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage);
                return BadRequest(ApiResponse<object>.Error("Validation failed", string.Join(", ", errors)));
            }

            // Create the lead entity
            var lead = request.ToLead();
            
            // In a real implementation, you would save to your lead repository here
            // For demonstration, we'll simulate the lead creation
            lead.Id = GenerateLeadId(); // Simulate database ID generation
            lead.CreatedAt = DateTime.UtcNow;
            lead.UpdatedAt = DateTime.UtcNow;

            // Record the lead creation in the history system
            var historyRecorded = await _leadHistoryService.RecordLeadCreationAsync(
                lead, 
                request.CreatedBy, 
                cancellationToken);

            if (!historyRecorded)
            {
                _logger.LogWarning("Failed to record history for lead creation: {LeadId}", lead.Id);
                // Note: In production, you might want to decide whether this should fail the entire operation
            }

            var response = new CreateLeadResponse
            {
                LeadId = lead.Id,
                Name = lead.Name,
                ContactNo = lead.ContactNo,
                CreatedAt = lead.CreatedAt,
                HistoryRecorded = historyRecorded,
                Message = historyRecorded 
                    ? "Lead created successfully with history tracking" 
                    : "Lead created successfully but history tracking failed"
            };

            _logger.LogInformation("Lead created: {LeadId} by {CreatedBy}, History: {HistoryRecorded}", 
                lead.Id, request.CreatedBy, historyRecorded);

            return CreatedAtAction(
                nameof(GetLeadHistory), 
                new { leadId = lead.Id }, 
                ApiResponse<CreateLeadResponse>.Success(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating lead: {Request}", request);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred while creating the lead"));
        }
    }

    /// <summary>
    /// Updates an existing lead with automatic change tracking.
    /// </summary>
    /// <param name="leadId">The ID of the lead to update</param>
    /// <param name="request">Lead update request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Update result with history tracking confirmation</returns>
    /// <response code="200">Lead updated successfully</response>
    /// <response code="400">Invalid lead data</response>
    /// <response code="404">Lead not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPut("{leadId}")]
    [ProducesResponseType(typeof(ApiResponse<UpdateLeadResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<UpdateLeadResponse>>> UpdateLead(
        [FromRoute] int leadId,
        [FromBody] UpdateLeadRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate the request
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage);
                return BadRequest(ApiResponse<object>.Error("Validation failed", string.Join(", ", errors)));
            }

            // In a real implementation, you would fetch the existing lead from your repository
            // For demonstration, we'll simulate fetching an existing lead
            var existingLead = await GetExistingLeadAsync(leadId, cancellationToken);
            if (existingLead == null)
            {
                return NotFound(ApiResponse<object>.Error("Lead not found", $"Lead with ID {leadId} was not found"));
            }

            // Create a copy of the existing lead for comparison
            var oldLead = existingLead.Clone();

            // Apply updates to the lead
            request.ApplyToLead(existingLead);
            existingLead.UpdatedAt = DateTime.UtcNow;
            existingLead.UpdatedBy = request.UpdatedBy;

            // In a real implementation, you would save the updated lead to your repository here

            // Record the changes in the history system
            var historyRecorded = await _leadHistoryService.RecordLeadChangesAsync(
                leadId, 
                oldLead, 
                existingLead, 
                request.UpdatedBy, 
                cancellationToken);

            if (!historyRecorded)
            {
                _logger.LogWarning("Failed to record history for lead update: {LeadId}", leadId);
            }

            var response = new UpdateLeadResponse
            {
                LeadId = leadId,
                UpdatedAt = existingLead.UpdatedAt,
                HistoryRecorded = historyRecorded,
                Message = historyRecorded 
                    ? "Lead updated successfully with change tracking" 
                    : "Lead updated successfully but change tracking failed"
            };

            _logger.LogInformation("Lead updated: {LeadId} by {UpdatedBy}, History: {HistoryRecorded}", 
                leadId, request.UpdatedBy, historyRecorded);

            return Ok(ApiResponse<UpdateLeadResponse>.Success(response));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating lead {LeadId}: {Request}", leadId, request);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred while updating the lead"));
        }
    }

    /// <summary>
    /// Retrieves history for a specific lead.
    /// </summary>
    /// <param name="leadId">The ID of the lead</param>
    /// <param name="request">History query parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated lead history</returns>
    /// <response code="200">Returns lead history</response>
    /// <response code="400">Invalid query parameters</response>
    /// <response code="404">Lead not found</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("{leadId}/history")]
    [ProducesResponseType(typeof(ApiResponse<HistoryResult<HistoryEntryDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<HistoryResult<HistoryEntryDto>>>> GetLeadHistory(
        [FromRoute] int leadId,
        [FromQuery] LeadHistoryQueryRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate that the lead exists (in a real implementation)
            var leadExists = await LeadExistsAsync(leadId, cancellationToken);
            if (!leadExists)
            {
                return NotFound(ApiResponse<object>.Error("Lead not found", $"Lead with ID {leadId} was not found"));
            }

            var result = await _leadHistoryService.GetLeadHistoryAsync(
                leadId,
                request.Page,
                request.PageSize,
                request.FieldName,
                request.StartDate,
                request.EndDate,
                cancellationToken);

            var dtoResult = result.Transform(entry => HistoryEntryDto.FromHistoryEntry(entry));

            _logger.LogDebug("Retrieved {Count} history entries for lead {LeadId}", 
                result.CurrentPageCount, leadId);

            return Ok(ApiResponse<HistoryResult<HistoryEntryDto>>.Success(dtoResult));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid query parameters for lead {LeadId}: {Request}", leadId, request);
            return BadRequest(ApiResponse<object>.Error("Invalid query parameters", ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving history for lead {LeadId}: {Request}", leadId, request);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred while retrieving lead history"));
        }
    }

    /// <summary>
    /// Records a specific field change for a lead.
    /// This endpoint allows manual history entry creation for special cases.
    /// </summary>
    /// <param name="leadId">The ID of the lead</param>
    /// <param name="request">Field change request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success status</returns>
    /// <response code="200">Field change recorded successfully</response>
    /// <response code="400">Invalid field change data</response>
    /// <response code="404">Lead not found</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("{leadId}/history/field-change")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<object>>> RecordFieldChange(
        [FromRoute] int leadId,
        [FromBody] RecordFieldChangeRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate that the lead exists
            var leadExists = await LeadExistsAsync(leadId, cancellationToken);
            if (!leadExists)
            {
                return NotFound(ApiResponse<object>.Error("Lead not found", $"Lead with ID {leadId} was not found"));
            }

            var success = await _leadHistoryService.RecordFieldChangeAsync(
                leadId,
                request.FieldName,
                request.OldValue,
                request.NewValue,
                request.ChangedBy,
                request.Metadata,
                cancellationToken);

            if (success)
            {
                _logger.LogDebug("Recorded manual field change for lead {LeadId}: {FieldName}", leadId, request.FieldName);
                return Ok(ApiResponse<object>.Success("Field change recorded successfully"));
            }
            else
            {
                _logger.LogWarning("Failed to record manual field change for lead {LeadId}: {FieldName}", leadId, request.FieldName);
                return BadRequest(ApiResponse<object>.Error("Failed to record field change", "Field change validation failed"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording field change for lead {LeadId}: {Request}", leadId, request);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred while recording the field change"));
        }
    }

    #region Private Helper Methods

    private static int GenerateLeadId()
    {
        // Simulate database ID generation
        return Random.Shared.Next(1000, 999999);
    }

    private async Task<Lead?> GetExistingLeadAsync(int leadId, CancellationToken cancellationToken)
    {
        // Simulate fetching from database
        // In a real implementation, this would query your lead repository
        await Task.Delay(10, cancellationToken); // Simulate database call

        return new Lead
        {
            Id = leadId,
            Name = "Existing Lead",
            ContactNo = "1234567890",
            Email = "<EMAIL>",
            CreatedAt = DateTime.UtcNow.AddDays(-30),
            CreatedBy = "system",
            UpdatedAt = DateTime.UtcNow.AddDays(-1),
            UpdatedBy = "system"
        };
    }

    private async Task<bool> LeadExistsAsync(int leadId, CancellationToken cancellationToken)
    {
        // Simulate checking if lead exists
        // In a real implementation, this would query your lead repository
        await Task.Delay(5, cancellationToken); // Simulate database call
        return leadId > 0; // Simple validation for demo
    }

    #endregion
}
