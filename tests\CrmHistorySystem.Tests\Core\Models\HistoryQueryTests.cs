using Xunit;
using FluentAssertions;
using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Tests.Core.Models;

public class HistoryQueryTests
{
    [Fact]
    public void GetRequiredTiers_NoDateRange_ReturnsAllTiers()
    {
        // Arrange
        var query = new HistoryQuery
        {
            LeadId = 1,
            Page = 1,
            PageSize = 50
        };

        // Act
        var tiers = query.GetRequiredTiers().ToList();

        // Assert
        tiers.Should().HaveCount(3);
        tiers.Should().Contain(StorageTier.Hot);
        tiers.Should().Contain(StorageTier.Warm);
        tiers.Should().Contain(StorageTier.Cold);
    }

    [Fact]
    public void GetRequiredTiers_RecentDateRange_ReturnsHotTierOnly()
    {
        // Arrange
        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-30),
            EndDate = DateTime.UtcNow,
            Page = 1,
            PageSize = 50
        };

        // Act
        var tiers = query.GetRequiredTiers().ToList();

        // Assert
        tiers.Should().HaveCount(1);
        tiers.Should().Contain(StorageTier.Hot);
    }

    [Fact]
    public void GetRequiredTiers_MediumDateRange_ReturnsWarmTierOnly()
    {
        // Arrange
        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-200),
            EndDate = DateTime.UtcNow.AddDays(-100),
            Page = 1,
            PageSize = 50
        };

        // Act
        var tiers = query.GetRequiredTiers().ToList();

        // Assert
        tiers.Should().HaveCount(1);
        tiers.Should().Contain(StorageTier.Warm);
    }

    [Fact]
    public void GetRequiredTiers_OldDateRange_ReturnsColdTierOnly()
    {
        // Arrange
        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-500),
            EndDate = DateTime.UtcNow.AddDays(-400),
            Page = 1,
            PageSize = 50
        };

        // Act
        var tiers = query.GetRequiredTiers().ToList();

        // Assert
        tiers.Should().HaveCount(1);
        tiers.Should().Contain(StorageTier.Cold);
    }

    [Fact]
    public void GetRequiredTiers_SpanningDateRange_ReturnsMultipleTiers()
    {
        // Arrange
        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-200),
            EndDate = DateTime.UtcNow.AddDays(-30),
            Page = 1,
            PageSize = 50
        };

        // Act
        var tiers = query.GetRequiredTiers().ToList();

        // Assert
        tiers.Should().HaveCount(2);
        tiers.Should().Contain(StorageTier.Hot);
        tiers.Should().Contain(StorageTier.Warm);
    }

    [Theory]
    [InlineData(1, 50, 0)]
    [InlineData(2, 50, 50)]
    [InlineData(3, 100, 200)]
    [InlineData(10, 25, 225)]
    public void GetSkipCount_VariousPageSizes_ReturnsCorrectSkipCount(int page, int pageSize, int expectedSkip)
    {
        // Arrange
        var query = new HistoryQuery
        {
            Page = page,
            PageSize = pageSize
        };

        // Act
        var skipCount = query.GetSkipCount();

        // Assert
        skipCount.Should().Be(expectedSkip);
    }

    [Theory]
    [InlineData(1, 50, true)]
    [InlineData(0, 50, false)] // Invalid page
    [InlineData(1, 0, false)] // Invalid page size
    [InlineData(1, 1001, false)] // Page size too large
    public void IsValid_VariousInputs_ReturnsExpectedResult(int page, int pageSize, bool expected)
    {
        // Arrange
        var query = new HistoryQuery
        {
            Page = page,
            PageSize = pageSize
        };

        // Act
        var isValid = query.IsValid();

        // Assert
        isValid.Should().Be(expected);
    }

    [Fact]
    public void IsValid_StartDateAfterEndDate_ReturnsFalse()
    {
        // Arrange
        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow.AddDays(-1),
            Page = 1,
            PageSize = 50
        };

        // Act
        var isValid = query.IsValid();

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void ForTier_HotTier_AdjustsDateRangeCorrectly()
    {
        // Arrange
        var originalQuery = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-200),
            EndDate = DateTime.UtcNow,
            LeadId = 1,
            Page = 1,
            PageSize = 50
        };

        // Act
        var tierQuery = originalQuery.ForTier(StorageTier.Hot);

        // Assert
        tierQuery.LeadId.Should().Be(originalQuery.LeadId);
        tierQuery.Page.Should().Be(originalQuery.Page);
        tierQuery.PageSize.Should().Be(originalQuery.PageSize);
        tierQuery.StartDate.Should().BeAfter(DateTime.UtcNow.AddDays(-91)); // Hot tier boundary
        tierQuery.EndDate.Should().Be(originalQuery.EndDate);
    }

    [Fact]
    public void ForTier_WarmTier_AdjustsDateRangeCorrectly()
    {
        // Arrange
        var originalQuery = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-500),
            EndDate = DateTime.UtcNow,
            LeadId = 1,
            Page = 1,
            PageSize = 50
        };

        // Act
        var tierQuery = originalQuery.ForTier(StorageTier.Warm);

        // Assert
        tierQuery.LeadId.Should().Be(originalQuery.LeadId);
        tierQuery.StartDate.Should().BeAfter(DateTime.UtcNow.AddDays(-366)); // Warm tier start boundary
        tierQuery.EndDate.Should().BeBefore(DateTime.UtcNow.AddDays(-89)); // Warm tier end boundary
    }

    [Fact]
    public void ForTier_ColdTier_AdjustsDateRangeCorrectly()
    {
        // Arrange
        var originalQuery = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-500),
            EndDate = DateTime.UtcNow,
            LeadId = 1,
            Page = 1,
            PageSize = 50
        };

        // Act
        var tierQuery = originalQuery.ForTier(StorageTier.Cold);

        // Assert
        tierQuery.LeadId.Should().Be(originalQuery.LeadId);
        tierQuery.StartDate.Should().Be(originalQuery.StartDate);
        tierQuery.EndDate.Should().BeBefore(DateTime.UtcNow.AddDays(-364)); // Cold tier boundary
    }

    [Fact]
    public void ToString_WithFilters_ReturnsFormattedString()
    {
        // Arrange
        var query = new HistoryQuery
        {
            LeadId = 123,
            FieldName = "TestField",
            ChangedBy = "TestUser",
            StartDate = new DateTime(2023, 1, 1),
            EndDate = new DateTime(2023, 12, 31),
            Page = 2,
            PageSize = 100
        };

        // Act
        var result = query.ToString();

        // Assert
        result.Should().Contain("Page 2");
        result.Should().Contain("Size 100");
        result.Should().Contain("LeadId=123");
        result.Should().Contain("FieldName=TestField");
        result.Should().Contain("ChangedBy=TestUser");
        result.Should().Contain("StartDate=2023-01-01");
        result.Should().Contain("EndDate=2023-12-31");
    }

    [Fact]
    public void ToString_NoFilters_ReturnsBasicString()
    {
        // Arrange
        var query = new HistoryQuery
        {
            Page = 1,
            PageSize = 50
        };

        // Act
        var result = query.ToString();

        // Assert
        result.Should().Contain("Page 1");
        result.Should().Contain("Size 50");
        result.Should().NotContain("WHERE");
    }
}
