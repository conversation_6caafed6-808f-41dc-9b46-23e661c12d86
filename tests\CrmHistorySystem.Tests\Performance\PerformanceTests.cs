using Xunit;
using NBomber.CSharp;
using NBomber.Http.CSharp;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using CrmHistorySystem.Api;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using System.Text.Json;
using FluentAssertions;

namespace CrmHistorySystem.Tests.Performance;

public class PerformanceTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;

    public PerformanceTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
    }

    [Fact]
    public async Task HistoryService_GetHistory_MeetsPerformanceRequirements()
    {
        // Arrange
        using var scope = _factory.Services.CreateScope();
        var historyService = scope.ServiceProvider.GetRequiredService<IHistoryService>();

        // Seed test data
        await SeedTestDataAsync(historyService, 10000);

        var query = new HistoryQuery
        {
            LeadId = 1,
            Page = 1,
            PageSize = 50
        };

        // Act & Assert - Performance test
        var scenario = Scenario.Create("get_history_performance", async context =>
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await historyService.GetHistoryAsync(query);
            stopwatch.Stop();

            // Verify performance requirement: < 100ms for hot tier queries
            if (result.QueriedTiers.Contains(StorageTier.Hot) && !result.QueriedTiers.Any(t => t != StorageTier.Hot))
            {
                stopwatch.ElapsedMilliseconds.Should().BeLessThan(100, "Hot tier queries should complete in under 100ms");
            }

            return Response.Ok();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 100, during: TimeSpan.FromSeconds(30))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert performance metrics
        var scnStats = stats.AllScenarioStats.First();
        scnStats.Ok.Response.Mean.Should().BeLessThan(100, "Mean response time should be under 100ms");
        scnStats.Ok.Response.Percentile95.Should().BeLessThan(200, "95th percentile should be under 200ms");
        scnStats.AllFailCount.Should().Be(0, "No requests should fail");
    }

    [Fact]
    public async Task HistoryService_AddHistoryBatch_MeetsPerformanceRequirements()
    {
        // Arrange
        using var scope = _factory.Services.CreateScope();
        var historyService = scope.ServiceProvider.GetRequiredService<IHistoryService>();

        // Act & Assert - Batch performance test
        var scenario = Scenario.Create("add_batch_performance", async context =>
        {
            var batch = CreateTestBatch(1000); // 1000 entries per batch
            
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var result = await historyService.AddHistoryBatchAsync(batch);
            stopwatch.Stop();

            // Verify batch processing performance
            result.IsSuccess.Should().BeTrue("Batch should process successfully");
            
            // Calculate throughput: should handle 10,000 entries per second
            var entriesPerSecond = (batch.Count * 1000.0) / stopwatch.ElapsedMilliseconds;
            entriesPerSecond.Should().BeGreaterThan(10000, "Should process at least 10,000 entries per second");

            return Response.Ok();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromSeconds(30))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert batch performance metrics
        var scnStats = stats.AllScenarioStats.First();
        scnStats.AllFailCount.Should().Be(0, "No batch operations should fail");
    }

    [Fact]
    public async Task Api_GetHistory_MeetsPerformanceRequirements()
    {
        // Arrange
        var client = _factory.CreateClient();

        // Seed test data
        using var scope = _factory.Services.CreateScope();
        var historyService = scope.ServiceProvider.GetRequiredService<IHistoryService>();
        await SeedTestDataAsync(historyService, 5000);

        // Act & Assert - API performance test
        var scenario = Scenario.Create("api_get_history_performance", async context =>
        {
            var leadId = Random.Shared.Next(1, 100);
            var response = await client.GetAsync($"/api/v1/history?leadId={leadId}&pageSize=50");
            
            response.IsSuccessStatusCode.Should().BeTrue("API should return success");
            
            return Response.Ok();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 1000, during: TimeSpan.FromSeconds(30))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert API performance metrics
        var scnStats = stats.AllScenarioStats.First();
        scnStats.Ok.Response.Mean.Should().BeLessThan(100, "API mean response time should be under 100ms");
        scnStats.Ok.Response.Percentile95.Should().BeLessThan(200, "API 95th percentile should be under 200ms");
        scnStats.AllFailCount.Should().Be(0, "No API requests should fail");
    }

    [Fact]
    public async Task Api_ConcurrentQueries_MaintainsPerformance()
    {
        // Arrange
        var client = _factory.CreateClient();

        // Seed test data
        using var scope = _factory.Services.CreateScope();
        var historyService = scope.ServiceProvider.GetRequiredService<IHistoryService>();
        await SeedTestDataAsync(historyService, 10000);

        // Act & Assert - Concurrent query test
        var scenario = Scenario.Create("concurrent_queries", async context =>
        {
            var leadId = Random.Shared.Next(1, 100);
            var fieldName = $"Field{Random.Shared.Next(1, 10)}";
            
            var response = await client.GetAsync($"/api/v1/history?leadId={leadId}&fieldName={fieldName}&pageSize=25");
            response.IsSuccessStatusCode.Should().BeTrue("Concurrent queries should succeed");
            
            return Response.Ok();
        })
        .WithLoadSimulations(
            Simulation.KeepConstant(copies: 1000, during: TimeSpan.FromSeconds(60))
        );

        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert concurrent performance
        var scnStats = stats.AllScenarioStats.First();
        scnStats.Ok.Response.Mean.Should().BeLessThan(150, "Mean response time under load should be under 150ms");
        scnStats.AllFailCount.Should().Be(0, "No concurrent queries should fail");
        
        // Verify throughput requirement: 1,000 concurrent operations
        var avgRps = scnStats.Ok.Request.Count / 60.0; // requests per second over 60 seconds
        avgRps.Should().BeGreaterThan(900, "Should handle at least 900 requests per second");
    }

    [Fact]
    public async Task Memory_Usage_StaysWithinLimits()
    {
        // Arrange
        using var scope = _factory.Services.CreateScope();
        var historyService = scope.ServiceProvider.GetRequiredService<IHistoryService>();

        // Seed large dataset
        await SeedTestDataAsync(historyService, 50000);

        var initialMemory = GC.GetTotalMemory(true);

        // Act - Perform memory-intensive operations
        var tasks = new List<Task>();
        for (int i = 0; i < 100; i++)
        {
            tasks.Add(Task.Run(async () =>
            {
                var query = new HistoryQuery
                {
                    LeadId = Random.Shared.Next(1, 1000),
                    Page = 1,
                    PageSize = 100
                };
                await historyService.GetHistoryAsync(query);
            }));
        }

        await Task.WhenAll(tasks);

        // Force garbage collection and measure memory
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var finalMemory = GC.GetTotalMemory(false);
        var memoryIncrease = finalMemory - initialMemory;

        // Assert memory usage requirement: < 2GB under normal load
        var memoryIncreaseGB = memoryIncrease / (1024.0 * 1024.0 * 1024.0);
        memoryIncreaseGB.Should().BeLessThan(2.0, "Memory usage should stay under 2GB");
    }

    private async Task SeedTestDataAsync(IHistoryService historyService, int entryCount)
    {
        var entries = new List<HistoryEntry>();
        var random = new Random(42); // Fixed seed for reproducible tests

        for (int i = 0; i < entryCount; i++)
        {
            var entry = new HistoryEntry
            {
                LeadId = random.Next(1, 100),
                FieldName = $"Field{random.Next(1, 10)}",
                OldValue = $"OldValue{i}",
                NewValue = $"NewValue{i}",
                ChangedAt = DateTime.UtcNow.AddDays(-random.Next(0, 400)),
                ChangedBy = $"User{random.Next(1, 20)}",
                Metadata = $"{{\"testId\": {i}}}"
            };
            entries.Add(entry);

            // Process in batches to avoid memory issues
            if (entries.Count >= 1000)
            {
                await historyService.AddHistoryBatchAsync(entries);
                entries.Clear();
            }
        }

        // Process remaining entries
        if (entries.Any())
        {
            await historyService.AddHistoryBatchAsync(entries);
        }
    }

    private HistoryBatch CreateTestBatch(int entryCount)
    {
        var batch = new HistoryBatch
        {
            CreatedBy = "PerformanceTest"
        };

        for (int i = 0; i < entryCount; i++)
        {
            var entry = new HistoryEntry
            {
                LeadId = Random.Shared.Next(1, 1000),
                FieldName = $"Field{Random.Shared.Next(1, 10)}",
                OldValue = $"OldValue{i}",
                NewValue = $"NewValue{i}",
                ChangedAt = DateTime.UtcNow,
                ChangedBy = "PerformanceTest"
            };
            batch.TryAddEntry(entry);
        }

        return batch;
    }
}
