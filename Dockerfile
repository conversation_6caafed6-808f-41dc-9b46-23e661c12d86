# Build stage
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src

# Copy csproj files and restore dependencies
COPY ["src/CrmHistorySystem.Api/CrmHistorySystem.Api.csproj", "src/CrmHistorySystem.Api/"]
COPY ["src/CrmHistorySystem.Infrastructure/CrmHistorySystem.Infrastructure.csproj", "src/CrmHistorySystem.Infrastructure/"]
COPY ["src/CrmHistorySystem.Core/CrmHistorySystem.Core.csproj", "src/CrmHistorySystem.Core/"]

RUN dotnet restore "src/CrmHistorySystem.Api/CrmHistorySystem.Api.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/src/CrmHistorySystem.Api"
RUN dotnet build "CrmHistorySystem.Api.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "CrmHistorySystem.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS final
WORKDIR /app

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy published application
COPY --from=publish /app/publish .

# Create logs directory and set permissions
RUN mkdir -p /app/logs && chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80
EXPOSE 443

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80

# Entry point
ENTRYPOINT ["dotnet", "CrmHistorySystem.Api.dll"]
