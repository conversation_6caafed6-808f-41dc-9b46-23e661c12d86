using System.Reflection;
using Microsoft.EntityFrameworkCore;
using DemoApp.Data;
using DemoApp.Models;

namespace DemoApp.Services;

/// <summary>
/// Service for tracking lead history changes.
/// </summary>
public class LeadHistoryService
{
    private readonly DemoDbContext _context;
    private readonly ILogger<LeadHistoryService> _logger;

    // Properties to exclude from automatic history tracking
    private static readonly HashSet<string> ExcludedProperties = new(StringComparer.OrdinalIgnoreCase)
    {
        "Id", "CreatedAt", "UpdatedAt", "Version", "RowVersion", "Timestamp"
    };

    public LeadHistoryService(DemoDbContext context, ILogger<LeadHistoryService> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Records the initial creation of a lead with all non-null property values.
    /// </summary>
    public async Task<bool> RecordLeadCreationAsync(Lead lead, string createdBy)
    {
        try
        {
            var changes = ExtractCreationChanges(lead);
            if (!changes.Any())
            {
                _logger.LogDebug("No properties with values found for lead creation history: {LeadId}", lead.Id);
                return true; // Not an error, just nothing to record
            }

            var entries = changes.Select(change => new HistoryEntry
            {
                LeadId = lead.Id,
                FieldName = change.FieldName,
                OldValue = change.OldValue,
                NewValue = change.NewValue,
                ChangedAt = DateTime.UtcNow,
                ChangedBy = createdBy,
                Metadata = $"{{\"operation\": \"create\", \"entityType\": \"Lead\"}}",
                Tier = "Hot"
            }).ToList();

            await _context.HistoryEntries.AddRangeAsync(entries);
            await _context.SaveChangesAsync();

            _logger.LogDebug("Recorded creation history for lead {LeadId}: {Count} entries", 
                lead.Id, entries.Count);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording lead creation history for lead {LeadId}", lead.Id);
            return false;
        }
    }

    /// <summary>
    /// Records changes to a lead by comparing old and new values.
    /// </summary>
    public async Task<bool> RecordLeadChangesAsync(int leadId, Lead oldLead, Lead newLead, string modifiedBy)
    {
        try
        {
            var changes = ExtractModificationChanges(oldLead, newLead);
            if (!changes.Any())
            {
                _logger.LogDebug("No changes detected for lead {LeadId}", leadId);
                return true; // Not an error, just no changes
            }

            var entries = changes.Select(change => new HistoryEntry
            {
                LeadId = leadId,
                FieldName = change.FieldName,
                OldValue = change.OldValue,
                NewValue = change.NewValue,
                ChangedAt = DateTime.UtcNow,
                ChangedBy = modifiedBy,
                Metadata = $"{{\"operation\": \"update\", \"entityType\": \"Lead\"}}",
                Tier = "Hot"
            }).ToList();

            await _context.HistoryEntries.AddRangeAsync(entries);
            await _context.SaveChangesAsync();

            _logger.LogDebug("Recorded modification history for lead {LeadId}: {Count} entries", 
                leadId, entries.Count);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording lead modification history for lead {LeadId}", leadId);
            return false;
        }
    }

    /// <summary>
    /// Retrieves history for a specific lead.
    /// </summary>
    public async Task<HistoryResult<HistoryEntry>> GetLeadHistoryAsync(
        int leadId,
        int page = 1,
        int pageSize = 50,
        string? fieldName = null,
        DateTime? startDate = null,
        DateTime? endDate = null)
    {
        try
        {
            var query = _context.HistoryEntries.AsQueryable();

            // Apply filters
            query = query.Where(e => e.LeadId == leadId);

            if (!string.IsNullOrEmpty(fieldName))
            {
                query = query.Where(e => e.FieldName == fieldName);
            }

            if (startDate.HasValue)
            {
                query = query.Where(e => e.ChangedAt >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(e => e.ChangedAt <= endDate.Value);
            }

            // Apply sorting
            query = query.OrderByDescending(e => e.ChangedAt);

            // Get total count for pagination
            var totalCount = await query.CountAsync();

            // Apply pagination
            var entries = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return new HistoryResult<HistoryEntry>
            {
                Data = entries,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize,
                CurrentPageCount = entries.Count
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving history for lead {LeadId}", leadId);
            throw;
        }
    }

    /// <summary>
    /// Gets storage statistics for monitoring.
    /// </summary>
    public async Task<StorageStats> GetStorageStatsAsync()
    {
        try
        {
            var totalEntries = await _context.HistoryEntries.CountAsync();
            var hotEntries = await _context.HistoryEntries.CountAsync(e => e.Tier == "Hot");
            var warmEntries = await _context.HistoryEntries.CountAsync(e => e.Tier == "Warm");
            var coldEntries = await _context.HistoryEntries.CountAsync(e => e.Tier == "Cold");

            var oldestEntry = await _context.HistoryEntries
                .OrderBy(e => e.ChangedAt)
                .Select(e => e.ChangedAt)
                .FirstOrDefaultAsync();

            var newestEntry = await _context.HistoryEntries
                .OrderByDescending(e => e.ChangedAt)
                .Select(e => e.ChangedAt)
                .FirstOrDefaultAsync();

            return new StorageStats
            {
                TotalEntries = totalEntries,
                HotTierEntries = hotEntries,
                WarmTierEntries = warmEntries,
                ColdTierEntries = coldEntries,
                OldestEntry = oldestEntry,
                NewestEntry = newestEntry,
                EstimatedSizeBytes = totalEntries * 500 // Rough estimate
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting storage statistics");
            throw;
        }
    }

    /// <summary>
    /// Extracts field changes for lead creation (all non-null properties).
    /// </summary>
    private List<FieldChange> ExtractCreationChanges(Lead lead)
    {
        var changes = new List<FieldChange>();
        var properties = typeof(Lead).GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            if (ShouldExcludeProperty(property))
                continue;

            var value = property.GetValue(lead);
            if (value != null)
            {
                var stringValue = ConvertToString(value);
                if (!string.IsNullOrEmpty(stringValue))
                {
                    changes.Add(new FieldChange
                    {
                        FieldName = property.Name,
                        OldValue = null,
                        NewValue = stringValue
                    });
                }
            }
        }

        return changes;
    }

    /// <summary>
    /// Extracts field changes by comparing old and new lead objects.
    /// </summary>
    private List<FieldChange> ExtractModificationChanges(Lead oldLead, Lead newLead)
    {
        var changes = new List<FieldChange>();
        var properties = typeof(Lead).GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            if (ShouldExcludeProperty(property))
                continue;

            var oldValue = property.GetValue(oldLead);
            var newValue = property.GetValue(newLead);

            var oldStringValue = ConvertToString(oldValue);
            var newStringValue = ConvertToString(newValue);

            if (!string.Equals(oldStringValue, newStringValue, StringComparison.Ordinal))
            {
                changes.Add(new FieldChange
                {
                    FieldName = property.Name,
                    OldValue = oldStringValue,
                    NewValue = newStringValue
                });
            }
        }

        return changes;
    }

    /// <summary>
    /// Determines if a property should be excluded from history tracking.
    /// </summary>
    private bool ShouldExcludeProperty(PropertyInfo property)
    {
        // Exclude properties that are not readable
        if (!property.CanRead)
            return true;

        // Exclude properties in the exclusion list
        if (ExcludedProperties.Contains(property.Name))
            return true;

        return false;
    }

    /// <summary>
    /// Converts a property value to its string representation for storage.
    /// </summary>
    private string? ConvertToString(object? value)
    {
        if (value == null)
            return null;

        return value switch
        {
            string str => str,
            DateTime dt => dt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            DateTimeOffset dto => dto.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            Guid guid => guid.ToString(),
            bool boolean => boolean.ToString().ToLowerInvariant(),
            _ => value.ToString()
        };
    }
}

/// <summary>
/// Represents a field change for tracking purposes.
/// </summary>
public class FieldChange
{
    /// <summary>
    /// The name of the field that changed.
    /// </summary>
    public string FieldName { get; set; } = string.Empty;

    /// <summary>
    /// The previous value of the field.
    /// </summary>
    public string? OldValue { get; set; }

    /// <summary>
    /// The new value of the field.
    /// </summary>
    public string? NewValue { get; set; }
}
