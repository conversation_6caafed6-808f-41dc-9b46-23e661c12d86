using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CrmHistorySystem.Infrastructure.Migrations
{
    /// <summary>
    /// Initial migration to create the CRM History System database schema.
    /// Creates optimized tables for hot and warm tier storage with proper indexing.
    /// </summary>
    public partial class InitialCreate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create Hot Tier Table
            migrationBuilder.CreateTable(
                name: "HistoryEntries_Hot",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    LeadId = table.Column<int>(type: "int", nullable: false),
                    FieldName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    OldValue = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true),
                    NewValue = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true),
                    ChangedAt = table.Column<DateTime>(type: "datetime2(3)", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    ChangedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Metadata = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HistoryEntries_Hot", x => x.Id)
                        .Annotation("SqlServer:Clustered", false);
                    table.CheckConstraint("CK_HistoryEntry_LeadId", "[LeadId] > 0");
                    table.CheckConstraint("CK_HistoryEntry_FieldName", "LEN([FieldName]) > 0");
                    table.CheckConstraint("CK_HistoryEntry_ChangedBy", "LEN([ChangedBy]) > 0");
                    table.CheckConstraint("CK_HistoryEntry_Values", "[OldValue] IS NOT NULL OR [NewValue] IS NOT NULL");
                });

            // Create Warm Tier Table
            migrationBuilder.CreateTable(
                name: "HistoryEntries_Warm",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    LeadId = table.Column<int>(type: "int", nullable: false),
                    FieldName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    OldValue = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true),
                    NewValue = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true),
                    ChangedAt = table.Column<DateTime>(type: "datetime2(3)", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    ChangedBy = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Metadata = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HistoryEntries_Warm", x => x.Id)
                        .Annotation("SqlServer:Clustered", false);
                    table.CheckConstraint("CK_HistoryEntry_Warm_LeadId", "[LeadId] > 0");
                    table.CheckConstraint("CK_HistoryEntry_Warm_FieldName", "LEN([FieldName]) > 0");
                    table.CheckConstraint("CK_HistoryEntry_Warm_ChangedBy", "LEN([ChangedBy]) > 0");
                    table.CheckConstraint("CK_HistoryEntry_Warm_Values", "[OldValue] IS NOT NULL OR [NewValue] IS NOT NULL");
                })
                .Annotation("SqlServer:DataCompression", "Page");

            // Hot Tier Indexes
            migrationBuilder.CreateIndex(
                name: "IX_HistoryEntries_Hot_LeadId_ChangedAt_Clustered",
                table: "HistoryEntries_Hot",
                columns: new[] { "LeadId", "ChangedAt" },
                descending: new[] { false, true })
                .Annotation("SqlServer:Clustered", true);

            migrationBuilder.CreateIndex(
                name: "IX_HistoryEntries_Hot_ChangedAt",
                table: "HistoryEntries_Hot",
                column: "ChangedAt",
                descending: true,
                filter: "[ChangedAt] >= DATEADD(day, -90, GETUTCDATE())");

            migrationBuilder.CreateIndex(
                name: "IX_HistoryEntries_Hot_FieldName_ChangedAt",
                table: "HistoryEntries_Hot",
                columns: new[] { "FieldName", "ChangedAt" },
                descending: new[] { false, true },
                filter: "[ChangedAt] >= DATEADD(day, -90, GETUTCDATE())");

            migrationBuilder.CreateIndex(
                name: "IX_HistoryEntries_Hot_ChangedBy_ChangedAt",
                table: "HistoryEntries_Hot",
                columns: new[] { "ChangedBy", "ChangedAt" },
                descending: new[] { false, true },
                filter: "[ChangedAt] >= DATEADD(day, -90, GETUTCDATE())");

            migrationBuilder.CreateIndex(
                name: "IX_HistoryEntries_Hot_Covering",
                table: "HistoryEntries_Hot",
                columns: new[] { "LeadId", "FieldName", "ChangedAt" },
                descending: new[] { false, false, true })
                .Annotation("SqlServer:Include", new[] { "OldValue", "NewValue", "ChangedBy" })
                .Annotation("SqlServer:Filter", "[ChangedAt] >= DATEADD(day, -90, GETUTCDATE())");

            // Warm Tier Indexes
            migrationBuilder.CreateIndex(
                name: "IX_HistoryEntries_Warm_ChangedAt_LeadId_Clustered",
                table: "HistoryEntries_Warm",
                columns: new[] { "ChangedAt", "LeadId" },
                descending: new[] { true, false })
                .Annotation("SqlServer:Clustered", true);

            migrationBuilder.CreateIndex(
                name: "IX_HistoryEntries_Warm_LeadId_ChangedAt",
                table: "HistoryEntries_Warm",
                columns: new[] { "LeadId", "ChangedAt" },
                descending: new[] { false, true });

            migrationBuilder.CreateIndex(
                name: "IX_HistoryEntries_Warm_FieldName",
                table: "HistoryEntries_Warm",
                column: "FieldName");

            // Create partition function and scheme for warm tier
            migrationBuilder.Sql(@"
                CREATE PARTITION FUNCTION [PF_HistoryByMonth] (DATETIME2(3))
                AS RANGE RIGHT FOR VALUES (
                    '2023-01-01', '2023-02-01', '2023-03-01', '2023-04-01', '2023-05-01', '2023-06-01',
                    '2023-07-01', '2023-08-01', '2023-09-01', '2023-10-01', '2023-11-01', '2023-12-01',
                    '2024-01-01', '2024-02-01', '2024-03-01', '2024-04-01', '2024-05-01', '2024-06-01',
                    '2024-07-01', '2024-08-01', '2024-09-01', '2024-10-01', '2024-11-01', '2024-12-01',
                    '2025-01-01', '2025-02-01', '2025-03-01', '2025-04-01', '2025-05-01', '2025-06-01',
                    '2025-07-01', '2025-08-01', '2025-09-01', '2025-10-01', '2025-11-01', '2025-12-01'
                );
            ");

            migrationBuilder.Sql(@"
                CREATE PARTITION SCHEME [PS_HistoryByMonth]
                AS PARTITION [PF_HistoryByMonth]
                ALL TO ([PRIMARY]);
            ");

            // Create unified view
            migrationBuilder.Sql(@"
                CREATE VIEW [dbo].[vw_HistoryEntries_All]
                AS
                SELECT 
                    [Id],
                    [LeadId],
                    [FieldName],
                    [OldValue],
                    [NewValue],
                    [ChangedAt],
                    [ChangedBy],
                    [Metadata],
                    'Hot' AS [StorageTier]
                FROM [dbo].[HistoryEntries_Hot]

                UNION ALL

                SELECT 
                    [Id],
                    [LeadId],
                    [FieldName],
                    [OldValue],
                    [NewValue],
                    [ChangedAt],
                    [ChangedBy],
                    [Metadata],
                    'Warm' AS [StorageTier]
                FROM [dbo].[HistoryEntries_Warm];
            ");

            // Create stored procedures
            migrationBuilder.Sql(@"
                CREATE PROCEDURE [dbo].[sp_BulkInsertHistoryHot]
                    @Entries NVARCHAR(MAX)
                AS
                BEGIN
                    SET NOCOUNT ON;
                    
                    BEGIN TRY
                        BEGIN TRANSACTION;
                        
                        INSERT INTO [dbo].[HistoryEntries_Hot] 
                        ([LeadId], [FieldName], [OldValue], [NewValue], [ChangedAt], [ChangedBy], [Metadata])
                        SELECT 
                            [LeadId],
                            [FieldName],
                            [OldValue],
                            [NewValue],
                            ISNULL(TRY_CAST([ChangedAt] AS DATETIME2(3)), GETUTCDATE()),
                            [ChangedBy],
                            [Metadata]
                        FROM OPENJSON(@Entries)
                        WITH (
                            [LeadId] INT '$.leadId',
                            [FieldName] NVARCHAR(100) '$.fieldName',
                            [OldValue] NVARCHAR(4000) '$.oldValue',
                            [NewValue] NVARCHAR(4000) '$.newValue',
                            [ChangedAt] NVARCHAR(50) '$.changedAt',
                            [ChangedBy] NVARCHAR(100) '$.changedBy',
                            [Metadata] NVARCHAR(2000) '$.metadata'
                        )
                        WHERE [LeadId] IS NOT NULL 
                          AND [FieldName] IS NOT NULL 
                          AND [ChangedBy] IS NOT NULL;
                        
                        COMMIT TRANSACTION;
                        SELECT @@ROWCOUNT AS [RowsInserted];
                        
                    END TRY
                    BEGIN CATCH
                        IF @@TRANCOUNT > 0
                            ROLLBACK TRANSACTION;
                        THROW;
                    END CATCH
                END;
            ");

            migrationBuilder.Sql(@"
                CREATE PROCEDURE [dbo].[sp_BulkInsertHistoryWarm]
                    @Entries NVARCHAR(MAX)
                AS
                BEGIN
                    SET NOCOUNT ON;
                    
                    BEGIN TRY
                        BEGIN TRANSACTION;
                        
                        INSERT INTO [dbo].[HistoryEntries_Warm] 
                        ([LeadId], [FieldName], [OldValue], [NewValue], [ChangedAt], [ChangedBy], [Metadata])
                        SELECT 
                            [LeadId],
                            [FieldName],
                            [OldValue],
                            [NewValue],
                            ISNULL(TRY_CAST([ChangedAt] AS DATETIME2(3)), GETUTCDATE()),
                            [ChangedBy],
                            [Metadata]
                        FROM OPENJSON(@Entries)
                        WITH (
                            [LeadId] INT '$.leadId',
                            [FieldName] NVARCHAR(100) '$.fieldName',
                            [OldValue] NVARCHAR(4000) '$.oldValue',
                            [NewValue] NVARCHAR(4000) '$.newValue',
                            [ChangedAt] NVARCHAR(50) '$.changedAt',
                            [ChangedBy] NVARCHAR(100) '$.changedBy',
                            [Metadata] NVARCHAR(2000) '$.metadata'
                        )
                        WHERE [LeadId] IS NOT NULL 
                          AND [FieldName] IS NOT NULL 
                          AND [ChangedBy] IS NOT NULL;
                        
                        COMMIT TRANSACTION;
                        SELECT @@ROWCOUNT AS [RowsInserted];
                        
                    END TRY
                    BEGIN CATCH
                        IF @@TRANCOUNT > 0
                            ROLLBACK TRANSACTION;
                        THROW;
                    END CATCH
                END;
            ");

            migrationBuilder.Sql(@"
                CREATE PROCEDURE [dbo].[sp_UpdateHistoryStatistics]
                AS
                BEGIN
                    SET NOCOUNT ON;
                    UPDATE STATISTICS [dbo].[HistoryEntries_Hot];
                    UPDATE STATISTICS [dbo].[HistoryEntries_Warm];
                    PRINT 'Statistics updated successfully';
                END;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop stored procedures
            migrationBuilder.Sql("DROP PROCEDURE IF EXISTS [dbo].[sp_UpdateHistoryStatistics];");
            migrationBuilder.Sql("DROP PROCEDURE IF EXISTS [dbo].[sp_BulkInsertHistoryWarm];");
            migrationBuilder.Sql("DROP PROCEDURE IF EXISTS [dbo].[sp_BulkInsertHistoryHot];");

            // Drop view
            migrationBuilder.Sql("DROP VIEW IF EXISTS [dbo].[vw_HistoryEntries_All];");

            // Drop partition scheme and function
            migrationBuilder.Sql("DROP PARTITION SCHEME IF EXISTS [PS_HistoryByMonth];");
            migrationBuilder.Sql("DROP PARTITION FUNCTION IF EXISTS [PF_HistoryByMonth];");

            // Drop tables
            migrationBuilder.DropTable(name: "HistoryEntries_Warm");
            migrationBuilder.DropTable(name: "HistoryEntries_Hot");
        }
    }
}
