namespace CrmHistorySystem.Core.Models;

/// <summary>
/// Represents the result of a history query with pagination information.
/// Provides comprehensive metadata for efficient client-side handling.
/// </summary>
/// <typeparam name="T">The type of data being returned</typeparam>
public class HistoryResult<T>
{
    /// <summary>
    /// The actual data returned by the query.
    /// </summary>
    public IEnumerable<T> Data { get; set; } = Enumerable.Empty<T>();

    /// <summary>
    /// Total number of records that match the query criteria across all pages.
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number (1-based).
    /// </summary>
    public int CurrentPage { get; set; }

    /// <summary>
    /// Number of records per page.
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages available.
    /// </summary>
    public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)TotalCount / PageSize) : 0;

    /// <summary>
    /// Indicates whether there are more pages available after the current page.
    /// </summary>
    public bool HasMore => CurrentPage < TotalPages;

    /// <summary>
    /// Indicates whether there is a previous page before the current page.
    /// </summary>
    public bool HasPrevious => CurrentPage > 1;

    /// <summary>
    /// Number of records in the current page.
    /// </summary>
    public int CurrentPageCount => Data?.Count() ?? 0;

    /// <summary>
    /// Query execution time in milliseconds for performance monitoring.
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// Storage tiers that were queried to produce this result.
    /// Useful for debugging and performance analysis.
    /// </summary>
    public IEnumerable<StorageTier> QueriedTiers { get; set; } = Enumerable.Empty<StorageTier>();

    /// <summary>
    /// Indicates whether the result was served from cache.
    /// </summary>
    public bool FromCache { get; set; }

    /// <summary>
    /// Cache expiration time if the result was served from cache.
    /// </summary>
    public DateTime? CacheExpiresAt { get; set; }

    /// <summary>
    /// Additional metadata about the query execution.
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Creates a successful result with data.
    /// </summary>
    public static HistoryResult<T> Success(
        IEnumerable<T> data, 
        int totalCount, 
        int currentPage, 
        int pageSize,
        long executionTimeMs = 0)
    {
        return new HistoryResult<T>
        {
            Data = data,
            TotalCount = totalCount,
            CurrentPage = currentPage,
            PageSize = pageSize,
            ExecutionTimeMs = executionTimeMs
        };
    }

    /// <summary>
    /// Creates an empty result for queries that return no data.
    /// </summary>
    public static HistoryResult<T> Empty(int currentPage = 1, int pageSize = 50)
    {
        return new HistoryResult<T>
        {
            Data = Enumerable.Empty<T>(),
            TotalCount = 0,
            CurrentPage = currentPage,
            PageSize = pageSize
        };
    }

    /// <summary>
    /// Creates a cached result with cache metadata.
    /// </summary>
    public static HistoryResult<T> FromCache(
        IEnumerable<T> data,
        int totalCount,
        int currentPage,
        int pageSize,
        DateTime cacheExpiresAt)
    {
        return new HistoryResult<T>
        {
            Data = data,
            TotalCount = totalCount,
            CurrentPage = currentPage,
            PageSize = pageSize,
            FromCache = true,
            CacheExpiresAt = cacheExpiresAt,
            ExecutionTimeMs = 0 // Cache hits are essentially instant
        };
    }

    /// <summary>
    /// Adds metadata about which tiers were queried.
    /// </summary>
    public HistoryResult<T> WithQueriedTiers(IEnumerable<StorageTier> tiers)
    {
        QueriedTiers = tiers;
        return this;
    }

    /// <summary>
    /// Adds custom metadata to the result.
    /// </summary>
    public HistoryResult<T> WithMetadata(string key, object value)
    {
        Metadata[key] = value;
        return this;
    }

    /// <summary>
    /// Transforms the data to a different type while preserving pagination metadata.
    /// </summary>
    public HistoryResult<TNew> Transform<TNew>(Func<T, TNew> transformer)
    {
        return new HistoryResult<TNew>
        {
            Data = Data.Select(transformer),
            TotalCount = TotalCount,
            CurrentPage = CurrentPage,
            PageSize = PageSize,
            ExecutionTimeMs = ExecutionTimeMs,
            QueriedTiers = QueriedTiers,
            FromCache = FromCache,
            CacheExpiresAt = CacheExpiresAt,
            Metadata = new Dictionary<string, object>(Metadata)
        };
    }

    /// <summary>
    /// Gets pagination information as a summary string.
    /// </summary>
    public string GetPaginationSummary()
    {
        if (TotalCount == 0)
            return "No records found";

        var start = (CurrentPage - 1) * PageSize + 1;
        var end = Math.Min(start + CurrentPageCount - 1, TotalCount);
        
        return $"Showing {start}-{end} of {TotalCount} records (Page {CurrentPage} of {TotalPages})";
    }

    /// <summary>
    /// Gets performance information as a summary string.
    /// </summary>
    public string GetPerformanceSummary()
    {
        var source = FromCache ? "cache" : "database";
        var tiers = QueriedTiers.Any() ? $" from {string.Join(", ", QueriedTiers)} tier(s)" : "";
        
        return $"Retrieved from {source} in {ExecutionTimeMs}ms{tiers}";
    }

    public override string ToString()
    {
        return $"HistoryResult<{typeof(T).Name}>: {GetPaginationSummary()}, {GetPerformanceSummary()}";
    }
}
