using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Extensions.Http;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Core.Configuration;
using CrmHistorySystem.Core.Extensions;
using CrmHistorySystem.Core.Validation;
using System.Diagnostics;

namespace CrmHistorySystem.Infrastructure.Services;

/// <summary>
/// Main implementation of the tiered history storage service.
/// Orchestrates queries and writes across hot, warm, and cold storage tiers.
/// </summary>
public class TieredHistoryService : IHistoryService
{
    private readonly IEnumerable<ITierStorage> _tierStorages;
    private readonly IHistoryCache _cache;
    private readonly ILogger<TieredHistoryService> _logger;
    private readonly HistoryOptions _options;
    private readonly HistoryEntryValidator _entryValidator;
    private readonly HistoryQueryValidator _queryValidator;
    private readonly HistoryBatchValidator _batchValidator;
    private readonly IAsyncPolicy _retryPolicy;

    public TieredHistoryService(
        IEnumerable<ITierStorage> tierStorages,
        IHistoryCache cache,
        ILogger<TieredHistoryService> logger,
        IOptions<HistoryOptions> options)
    {
        _tierStorages = tierStorages;
        _cache = cache;
        _logger = logger;
        _options = options.Value;
        _entryValidator = new HistoryEntryValidator();
        _queryValidator = new HistoryQueryValidator();
        _batchValidator = new HistoryBatchValidator();
        _retryPolicy = CreateRetryPolicy();
    }

    /// <summary>
    /// Retrieves history entries based on the specified query criteria.
    /// Automatically routes queries across appropriate storage tiers.
    /// </summary>
    public async Task<HistoryResult<HistoryEntry>> GetHistoryAsync(
        HistoryQuery query, 
        CancellationToken cancellationToken = default)
    {
        // Validate query
        var validationResult = await _queryValidator.ValidateAsync(query, cancellationToken);
        if (!validationResult.IsValid)
        {
            var errors = string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage));
            throw new ArgumentException($"Invalid query: {errors}");
        }

        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Check cache first
            var cacheKey = _cache.GenerateCacheKey(query);
            var cachedResult = await _cache.GetAsync(cacheKey, cancellationToken);
            
            if (cachedResult != null)
            {
                _logger.LogDebug("Cache hit for query: {CacheKey}", cacheKey);
                return cachedResult;
            }

            // Determine which tiers need to be queried
            var requiredTiers = query.GetRequiredTiers().ToList();
            _logger.LogDebug("Query requires tiers: {Tiers}", string.Join(", ", requiredTiers));

            // Query each required tier
            var tierResults = new List<HistoryResult<HistoryEntry>>();
            
            foreach (var tier in requiredTiers.OrderBy(t => (int)t)) // Query fastest tiers first
            {
                var tierStorage = GetTierStorage(tier);
                if (tierStorage != null)
                {
                    var tierQuery = query.ForTier(tier);
                    var tierResult = await _retryPolicy.ExecuteAsync(async () =>
                        await tierStorage.QueryAsync(tierQuery, cancellationToken));
                    
                    tierResults.Add(tierResult);
                }
            }

            // Combine results from all tiers
            var combinedResult = CombineTierResults(tierResults, query);
            
            stopwatch.Stop();
            combinedResult.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;

            // Cache the result if it's worth caching
            if (ShouldCacheResult(combinedResult, query))
            {
                var cacheExpiration = GetCacheExpiration(requiredTiers);
                await _cache.SetAsync(cacheKey, combinedResult, cacheExpiration, cancellationToken);
            }

            _logger.LogDebug("Query completed: {Count} results from {TierCount} tiers in {ElapsedMs}ms", 
                combinedResult.CurrentPageCount, requiredTiers.Count, stopwatch.ElapsedMilliseconds);

            return combinedResult;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error executing history query: {Query}", query);
            throw;
        }
    }

    /// <summary>
    /// Adds a single history entry to the appropriate storage tier.
    /// </summary>
    public async Task<bool> AddHistoryEntryAsync(
        HistoryEntry entry, 
        CancellationToken cancellationToken = default)
    {
        // Validate entry
        var validationResult = await _entryValidator.ValidateAsync(entry, cancellationToken);
        if (!validationResult.IsValid)
        {
            var errors = string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage));
            _logger.LogWarning("Invalid history entry: {Errors}", errors);
            return false;
        }

        try
        {
            var tier = entry.GetStorageTier();
            var tierStorage = GetTierStorage(tier);
            
            if (tierStorage == null)
            {
                _logger.LogError("No storage implementation found for tier: {Tier}", tier);
                return false;
            }

            var result = await _retryPolicy.ExecuteAsync(async () =>
                await tierStorage.AddEntriesAsync(new[] { entry }, cancellationToken));

            if (result)
            {
                // Invalidate relevant cache entries
                await InvalidateCacheForEntry(entry, cancellationToken);
                
                _logger.LogDebug("Added history entry to tier {Tier}: {Entry}", tier, entry);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding history entry: {Entry}", entry);
            throw;
        }
    }

    /// <summary>
    /// Adds multiple history entries in a single batch operation.
    /// </summary>
    public async Task<bool> AddHistoryBatchAsync(
        IEnumerable<HistoryEntry> entries, 
        CancellationToken cancellationToken = default)
    {
        var entryList = entries.ToList();
        if (!entryList.Any())
            return true;

        // Create and validate batch
        var batch = HistoryBatch.Create(entryList, "System", "Bulk insert operation");
        return (await AddHistoryBatchAsync(batch, cancellationToken)).IsSuccess;
    }

    /// <summary>
    /// Adds a validated batch of history entries.
    /// </summary>
    public async Task<BatchOperationResult> AddHistoryBatchAsync(
        HistoryBatch batch, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Validate batch
            var validationResult = await _batchValidator.ValidateAsync(batch, cancellationToken);
            if (!validationResult.IsValid)
            {
                var errors = string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage));
                return BatchOperationResult.Failure(batch.BatchId, batch.Count, $"Validation failed: {errors}");
            }

            // Group entries by tier for efficient processing
            var entriesByTier = batch.GroupByTier();
            var results = new List<BatchOperationResult>();

            foreach (var tierGroup in entriesByTier)
            {
                var tierStorage = GetTierStorage(tierGroup.Key);
                if (tierStorage == null)
                {
                    _logger.LogError("No storage implementation found for tier: {Tier}", tierGroup.Key);
                    continue;
                }

                var tierResult = await _retryPolicy.ExecuteAsync(async () =>
                    await tierStorage.AddEntriesAsync(tierGroup.Value, cancellationToken));

                if (tierResult)
                {
                    results.Add(BatchOperationResult.Success(batch.BatchId, tierGroup.Value.Count, 0));
                }
                else
                {
                    results.Add(BatchOperationResult.Failure(batch.BatchId, tierGroup.Value.Count, 
                        $"Failed to add entries to tier {tierGroup.Key}"));
                }
            }

            stopwatch.Stop();

            // Combine results
            var combinedResult = CombineBatchResults(batch.BatchId, results, stopwatch.ElapsedMilliseconds);

            if (combinedResult.IsSuccess)
            {
                // Invalidate cache for affected entries
                await InvalidateCacheForBatch(batch, cancellationToken);
                
                _logger.LogDebug("Successfully processed batch {BatchId}: {SuccessCount}/{TotalCount} entries", 
                    batch.BatchId, combinedResult.SuccessCount, combinedResult.TotalCount);
            }

            return combinedResult;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error processing batch {BatchId}", batch.BatchId);
            return BatchOperationResult.Failure(batch.BatchId, batch.Count, ex.Message);
        }
    }

    /// <summary>
    /// Archives old history entries from hot/warm tiers to cold storage.
    /// </summary>
    public async Task<int> ArchiveOldEntriesAsync(
        DateTime cutoffDate, 
        CancellationToken cancellationToken = default)
    {
        var totalArchived = 0;
        
        try
        {
            // Archive from hot to warm tier
            var hotStorage = GetTierStorage(StorageTier.Hot);
            if (hotStorage != null)
            {
                var hotCutoff = DateTime.UtcNow.AddDays(-_options.HotTierRetentionDays);
                var hotArchived = await hotStorage.MoveEntriesAsync(hotCutoff, StorageTier.Warm, cancellationToken);
                totalArchived += hotArchived;
                
                _logger.LogInformation("Archived {Count} entries from hot to warm tier", hotArchived);
            }

            // Archive from warm to cold tier
            var warmStorage = GetTierStorage(StorageTier.Warm);
            if (warmStorage != null)
            {
                var warmCutoff = DateTime.UtcNow.AddDays(-_options.WarmTierRetentionDays);
                var warmArchived = await warmStorage.MoveEntriesAsync(warmCutoff, StorageTier.Cold, cancellationToken);
                totalArchived += warmArchived;
                
                _logger.LogInformation("Archived {Count} entries from warm to cold tier", warmArchived);
            }

            return totalArchived;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during archival process");
            throw;
        }
    }

    /// <summary>
    /// Gets statistics about history storage across all tiers.
    /// </summary>
    public async Task<HistoryStorageStats> GetStorageStatsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var stats = new HistoryStorageStats();

            foreach (var tierStorage in _tierStorages)
            {
                var tierStats = await tierStorage.GetStatsAsync(cancellationToken);
                stats.TierStats[tierStorage.Tier] = tierStats;
            }

            _logger.LogDebug("Retrieved storage stats: {TotalEntries} total entries across {TierCount} tiers",
                stats.TotalEntries, stats.TierStats.Count);

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting storage statistics");
            throw;
        }
    }

    /// <summary>
    /// Validates the integrity of history data across all tiers.
    /// </summary>
    public async Task<DataIntegrityResult> ValidateDataIntegrityAsync(CancellationToken cancellationToken = default)
    {
        var result = new DataIntegrityResult();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            foreach (var tierStorage in _tierStorages)
            {
                var tierStats = await tierStorage.GetStatsAsync(cancellationToken);
                result.RecordsValidated[tierStorage.Tier] = tierStats.EntryCount;

                // Basic integrity checks
                if (tierStats.EntryCount < 0)
                {
                    result.AddIssue(IntegrityIssueType.Error,
                        $"Negative entry count in tier {tierStorage.Tier}", tierStorage.Tier);
                }

                if (tierStats.SizeBytes < 0)
                {
                    result.AddIssue(IntegrityIssueType.Error,
                        $"Negative size in tier {tierStorage.Tier}", tierStorage.Tier);
                }

                // Check for reasonable data distribution
                if (tierStorage.Tier == StorageTier.Hot && tierStats.EntryCount == 0)
                {
                    result.AddIssue(IntegrityIssueType.Warning,
                        "Hot tier has no entries - this may indicate a data loading issue", tierStorage.Tier);
                }
            }

            stopwatch.Stop();
            result.ValidationTimeMs = stopwatch.ElapsedMilliseconds;

            _logger.LogInformation("Data integrity validation completed: {Status} in {ElapsedMs}ms",
                result.IsValid ? "VALID" : "INVALID", stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error during data integrity validation");
            result.AddIssue(IntegrityIssueType.Critical, $"Validation failed: {ex.Message}");
            result.ValidationTimeMs = stopwatch.ElapsedMilliseconds;
            return result;
        }
    }

    /// <summary>
    /// Clears cached data for the specified criteria.
    /// </summary>
    public async Task<bool> ClearCacheAsync(
        int? leadId = null,
        string? fieldName = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (leadId.HasValue)
            {
                await ((RedisHistoryCache)_cache).ClearLeadCacheAsync(leadId.Value, cancellationToken);
            }

            if (!string.IsNullOrEmpty(fieldName))
            {
                await ((RedisHistoryCache)_cache).ClearFieldCacheAsync(fieldName, cancellationToken);
            }

            if (!leadId.HasValue && string.IsNullOrEmpty(fieldName))
            {
                // Clear all cache
                await _cache.RemoveAsync($"{_options.Cache.KeyPrefix}*", cancellationToken);
            }

            _logger.LogDebug("Cleared cache for LeadId: {LeadId}, FieldName: {FieldName}", leadId, fieldName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
            return false;
        }
    }

    #region Private Helper Methods

    private ITierStorage? GetTierStorage(StorageTier tier)
    {
        return _tierStorages.FirstOrDefault(ts => ts.Tier == tier);
    }

    private IAsyncPolicy CreateRetryPolicy()
    {
        return Policy
            .Handle<Exception>()
            .WaitAndRetryAsync(
                retryCount: _options.MaxRetryAttempts,
                sleepDurationProvider: retryAttempt => TimeSpan.FromMilliseconds(
                    Math.Min(_options.InitialRetryDelayMs * Math.Pow(2, retryAttempt - 1), _options.MaxRetryDelayMs)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Retry attempt {RetryCount} after {Delay}ms: {Exception}",
                        retryCount, timespan.TotalMilliseconds, outcome.Exception?.Message);
                });
    }

    private HistoryResult<HistoryEntry> CombineTierResults(
        List<HistoryResult<HistoryEntry>> tierResults,
        HistoryQuery query)
    {
        if (!tierResults.Any())
            return HistoryResult<HistoryEntry>.Empty(query.Page, query.PageSize);

        // Combine all data from different tiers
        var allEntries = new List<HistoryEntry>();
        var queriedTiers = new List<StorageTier>();

        foreach (var result in tierResults)
        {
            allEntries.AddRange(result.Data);
            queriedTiers.AddRange(result.QueriedTiers);
        }

        // Sort combined results
        var sortedEntries = query.SortOrder switch
        {
            HistorySortOrder.ChangedAtAscending => allEntries.OrderBy(e => e.ChangedAt).ToList(),
            HistorySortOrder.FieldNameThenChangedAt => allEntries.OrderBy(e => e.FieldName).ThenByDescending(e => e.ChangedAt).ToList(),
            HistorySortOrder.ChangedByThenChangedAt => allEntries.OrderBy(e => e.ChangedBy).ThenByDescending(e => e.ChangedAt).ToList(),
            _ => allEntries.OrderByDescending(e => e.ChangedAt).ToList()
        };

        // Apply pagination to combined results
        var totalCount = sortedEntries.Count;
        var pagedEntries = sortedEntries
            .Skip(query.GetSkipCount())
            .Take(query.PageSize)
            .ToList();

        var result = HistoryResult<HistoryEntry>.Success(
            pagedEntries,
            totalCount,
            query.Page,
            query.PageSize);

        result.QueriedTiers = queriedTiers.Distinct();
        return result;
    }

    private BatchOperationResult CombineBatchResults(
        Guid batchId,
        List<BatchOperationResult> results,
        long totalProcessingTime)
    {
        var combinedResult = new BatchOperationResult
        {
            BatchId = batchId,
            ProcessingTimeMs = totalProcessingTime
        };

        foreach (var result in results)
        {
            combinedResult.SuccessCount += result.SuccessCount;
            combinedResult.FailureCount += result.FailureCount;
            combinedResult.TotalCount += result.TotalCount;
            combinedResult.Errors.AddRange(result.Errors);
            combinedResult.Warnings.AddRange(result.Warnings);
        }

        return combinedResult;
    }

    private bool ShouldCacheResult(HistoryResult<HistoryEntry> result, HistoryQuery query)
    {
        // Don't cache empty results
        if (!result.Data.Any())
            return false;

        // Don't cache very large results
        if (result.CurrentPageCount > 100)
            return false;

        // Don't cache results from cold tier (they're already slow)
        if (result.QueriedTiers.Contains(StorageTier.Cold))
            return false;

        // Cache results that took significant time to compute
        return result.ExecutionTimeMs > 50;
    }

    private TimeSpan GetCacheExpiration(List<StorageTier> tiers)
    {
        // Use the shortest recommended TTL from the queried tiers
        var minTtl = tiers.Select(t => t.GetRecommendedCacheTtl()).Min();
        return minTtl;
    }

    private async Task InvalidateCacheForEntry(HistoryEntry entry, CancellationToken cancellationToken)
    {
        try
        {
            // Invalidate cache entries that might contain this entry
            await ((RedisHistoryCache)_cache).ClearLeadCacheAsync(entry.LeadId, cancellationToken);
            await ((RedisHistoryCache)_cache).ClearFieldCacheAsync(entry.FieldName, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to invalidate cache for entry: {Entry}", entry);
        }
    }

    private async Task InvalidateCacheForBatch(HistoryBatch batch, CancellationToken cancellationToken)
    {
        try
        {
            var leadIds = batch.Entries.Select(e => e.LeadId).Distinct();
            var fieldNames = batch.Entries.Select(e => e.FieldName).Distinct();

            foreach (var leadId in leadIds)
            {
                await ((RedisHistoryCache)_cache).ClearLeadCacheAsync(leadId, cancellationToken);
            }

            foreach (var fieldName in fieldNames)
            {
                await ((RedisHistoryCache)_cache).ClearFieldCacheAsync(fieldName, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to invalidate cache for batch: {BatchId}", batch.BatchId);
        }
    }

    #endregion
}
