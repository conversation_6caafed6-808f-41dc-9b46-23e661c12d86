using Microsoft.AspNetCore.Mvc;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Api.Models;
using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Api.Controllers;

/// <summary>
/// API controller for CRM history operations.
/// Provides high-performance access to tiered history storage.
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Produces("application/json")]
public class HistoryController : ControllerBase
{
    private readonly IHistoryService _historyService;
    private readonly ILogger<HistoryController> _logger;

    public HistoryController(
        IHistoryService historyService,
        ILogger<HistoryController> logger)
    {
        _historyService = historyService;
        _logger = logger;
    }

    /// <summary>
    /// Retrieves history entries based on query criteria.
    /// </summary>
    /// <param name="request">Query parameters for filtering and pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated history results</returns>
    /// <response code="200">Returns the history entries</response>
    /// <response code="400">Invalid query parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<HistoryResult<HistoryEntryDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<HistoryResult<HistoryEntryDto>>>> GetHistory(
        [FromQuery] HistoryQueryRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = request.ToHistoryQuery();
            var result = await _historyService.GetHistoryAsync(query, cancellationToken);
            
            var dtoResult = result.Transform(entry => HistoryEntryDto.FromHistoryEntry(entry));
            
            _logger.LogDebug("Retrieved {Count} history entries for query: {Query}", 
                result.CurrentPageCount, query);

            return Ok(ApiResponse<HistoryResult<HistoryEntryDto>>.Success(dtoResult));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid query parameters: {Request}", request);
            return BadRequest(ApiResponse<object>.Error("Invalid query parameters", ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving history: {Request}", request);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred while retrieving history"));
        }
    }

    /// <summary>
    /// Adds a single history entry.
    /// </summary>
    /// <param name="request">History entry to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success status</returns>
    /// <response code="201">History entry created successfully</response>
    /// <response code="400">Invalid entry data</response>
    /// <response code="500">Internal server error</response>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<object>>> AddHistoryEntry(
        [FromBody] CreateHistoryEntryRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var entry = request.ToHistoryEntry();
            var success = await _historyService.AddHistoryEntryAsync(entry, cancellationToken);

            if (success)
            {
                _logger.LogDebug("Added history entry: {Entry}", entry);
                return CreatedAtAction(nameof(GetHistory), 
                    new { leadId = entry.LeadId }, 
                    ApiResponse<object>.Success("History entry created successfully"));
            }
            else
            {
                _logger.LogWarning("Failed to add history entry: {Entry}", entry);
                return BadRequest(ApiResponse<object>.Error("Failed to add history entry", "Entry validation failed"));
            }
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid entry data: {Request}", request);
            return BadRequest(ApiResponse<object>.Error("Invalid entry data", ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding history entry: {Request}", request);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred while adding the history entry"));
        }
    }

    /// <summary>
    /// Adds multiple history entries in a batch.
    /// </summary>
    /// <param name="request">Batch of history entries to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Batch operation result</returns>
    /// <response code="200">Batch processed successfully</response>
    /// <response code="400">Invalid batch data</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("batch")]
    [ProducesResponseType(typeof(ApiResponse<BatchOperationResultDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<BatchOperationResultDto>>> AddHistoryBatch(
        [FromBody] CreateHistoryBatchRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var batch = request.ToHistoryBatch();
            var result = await _historyService.AddHistoryBatchAsync(batch, cancellationToken);
            
            var resultDto = BatchOperationResultDto.FromBatchOperationResult(result);
            
            _logger.LogDebug("Processed batch {BatchId}: {SuccessCount}/{TotalCount} successful", 
                result.BatchId, result.SuccessCount, result.TotalCount);

            return Ok(ApiResponse<BatchOperationResultDto>.Success(resultDto));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid batch data: {Request}", request);
            return BadRequest(ApiResponse<object>.Error("Invalid batch data", ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing batch: {Request}", request);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred while processing the batch"));
        }
    }

    /// <summary>
    /// Gets storage statistics across all tiers.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Storage statistics</returns>
    /// <response code="200">Returns storage statistics</response>
    /// <response code="500">Internal server error</response>
    [HttpGet("stats")]
    [ProducesResponseType(typeof(ApiResponse<HistoryStorageStatsDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<HistoryStorageStatsDto>>> GetStorageStats(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var stats = await _historyService.GetStorageStatsAsync(cancellationToken);
            var statsDto = HistoryStorageStatsDto.FromHistoryStorageStats(stats);
            
            _logger.LogDebug("Retrieved storage stats: {TotalEntries} total entries", stats.TotalEntries);

            return Ok(ApiResponse<HistoryStorageStatsDto>.Success(statsDto));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving storage stats");
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred while retrieving storage statistics"));
        }
    }

    /// <summary>
    /// Validates data integrity across all tiers.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Data integrity validation result</returns>
    /// <response code="200">Returns validation result</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("validate")]
    [ProducesResponseType(typeof(ApiResponse<DataIntegrityResultDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<DataIntegrityResultDto>>> ValidateDataIntegrity(
        CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await _historyService.ValidateDataIntegrityAsync(cancellationToken);
            var resultDto = DataIntegrityResultDto.FromDataIntegrityResult(result);
            
            _logger.LogInformation("Data integrity validation completed: {Status}", 
                result.IsValid ? "VALID" : "INVALID");

            return Ok(ApiResponse<DataIntegrityResultDto>.Success(resultDto));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during data integrity validation");
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred during validation"));
        }
    }

    /// <summary>
    /// Archives old history entries to cold storage.
    /// </summary>
    /// <param name="request">Archival parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of entries archived</returns>
    /// <response code="200">Archival completed successfully</response>
    /// <response code="400">Invalid archival parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("archive")]
    [ProducesResponseType(typeof(ApiResponse<ArchivalResultDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<ArchivalResultDto>>> ArchiveOldEntries(
        [FromBody] ArchiveRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (request.CutoffDate >= DateTime.UtcNow)
            {
                return BadRequest(ApiResponse<object>.Error("Invalid cutoff date", "Cutoff date must be in the past"));
            }

            var archivedCount = await _historyService.ArchiveOldEntriesAsync(request.CutoffDate, cancellationToken);
            
            var result = new ArchivalResultDto
            {
                CutoffDate = request.CutoffDate,
                ArchivedCount = archivedCount,
                CompletedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Archived {Count} entries with cutoff date {CutoffDate}", 
                archivedCount, request.CutoffDate);

            return Ok(ApiResponse<ArchivalResultDto>.Success(result));
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid archival request: {Request}", request);
            return BadRequest(ApiResponse<object>.Error("Invalid request", ex.Message));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during archival: {Request}", request);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred during archival"));
        }
    }

    /// <summary>
    /// Clears cache for specified criteria.
    /// </summary>
    /// <param name="request">Cache clearing parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success status</returns>
    /// <response code="200">Cache cleared successfully</response>
    /// <response code="500">Internal server error</response>
    [HttpDelete("cache")]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ApiResponse<object>>> ClearCache(
        [FromQuery] ClearCacheRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var success = await _historyService.ClearCacheAsync(
                request.LeadId, 
                request.FieldName, 
                cancellationToken);

            if (success)
            {
                _logger.LogDebug("Cache cleared for LeadId: {LeadId}, FieldName: {FieldName}", 
                    request.LeadId, request.FieldName);
                return Ok(ApiResponse<object>.Success("Cache cleared successfully"));
            }
            else
            {
                _logger.LogWarning("Failed to clear cache for LeadId: {LeadId}, FieldName: {FieldName}", 
                    request.LeadId, request.FieldName);
                return StatusCode(500, ApiResponse<object>.Error("Cache clear failed", "Failed to clear cache"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache: {Request}", request);
            return StatusCode(500, ApiResponse<object>.Error("Internal server error", "An error occurred while clearing cache"));
        }
    }
}
