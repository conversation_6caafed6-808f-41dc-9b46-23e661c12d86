using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add Entity Framework with Supabase PostgreSQL
var connectionString = "Host=db.ezcwxlffzmqwdzpqxtan.supabase.co;Database=********;Username=********;Password=********;Port=5432;SSL Mode=Require;Trust Server Certificate=true";
builder.Services.AddDbContext<CrmDbContext>(options =>
    options.UseNpgsql(connectionString));

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "CRM History Demo API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();

// Initialize database
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<CrmDbContext>();
    
    try
    {
        // Ensure database is created
        await context.Database.EnsureCreatedAsync();
        
        // Add sample data if database is empty
        if (!await context.Leads.AnyAsync())
        {
            var sampleLeads = new[]
            {
                new Lead
                {
                    Name = "John Doe",
                    ContactNo = "**********",
                    Email = "<EMAIL>",
                    Notes = "Interested in premium properties",
                    ChosenProject = "Luxury Apartments",
                    AssignTo = Guid.NewGuid(),
                    CreatedBy = "system",
                    UpdatedBy = "system"
                },
                new Lead
                {
                    Name = "Jane Smith",
                    ContactNo = "**********",
                    Email = "<EMAIL>",
                    Notes = "Looking for 3BHK apartment",
                    ChosenProject = "Premium Villas",
                    AssignTo = Guid.NewGuid(),
                    CreatedBy = "system",
                    UpdatedBy = "system"
                }
            };

            context.Leads.AddRange(sampleLeads);
            await context.SaveChangesAsync();

            // Create history entries for the sample leads
            foreach (var lead in sampleLeads)
            {
                var historyEntries = new[]
                {
                    new HistoryEntry { LeadId = lead.Id, FieldName = "Name", OldValue = null, NewValue = lead.Name, ChangedBy = "system", Tier = "Hot" },
                    new HistoryEntry { LeadId = lead.Id, FieldName = "ContactNo", OldValue = null, NewValue = lead.ContactNo, ChangedBy = "system", Tier = "Hot" },
                    new HistoryEntry { LeadId = lead.Id, FieldName = "Email", OldValue = null, NewValue = lead.Email, ChangedBy = "system", Tier = "Hot" },
                    new HistoryEntry { LeadId = lead.Id, FieldName = "Notes", OldValue = null, NewValue = lead.Notes, ChangedBy = "system", Tier = "Hot" },
                    new HistoryEntry { LeadId = lead.Id, FieldName = "ChosenProject", OldValue = null, NewValue = lead.ChosenProject, ChangedBy = "system", Tier = "Hot" }
                };
                
                context.HistoryEntries.AddRange(historyEntries);
            }
            
            await context.SaveChangesAsync();
            Console.WriteLine("✅ Sample data created with history tracking!");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"❌ Error initializing database: {ex.Message}");
        Console.WriteLine("The app will continue running, but database features may not work.");
    }
}

Console.WriteLine("🚀 CRM History System Demo API is running!");
Console.WriteLine("📖 Swagger UI available at: http://localhost:5000");
Console.WriteLine("🔍 Try the following endpoints:");
Console.WriteLine("   GET  /api/leads - Get all leads");
Console.WriteLine("   POST /api/leads - Create a new lead");
Console.WriteLine("   GET  /api/leads/{id}/history - Get lead history");

app.Run();

// Models
public class Lead
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    [Required]
    public string ContactNo { get; set; } = string.Empty;
    public string? AlternateContactNo { get; set; }
    public string? Email { get; set; }
    public string? Notes { get; set; }
    public DateTime? ScheduledDate { get; set; }
    public string? ChosenProject { get; set; }
    public Guid AssignTo { get; set; }
    public int ShareCount { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public string CreatedBy { get; set; } = string.Empty;
    public string UpdatedBy { get; set; } = string.Empty;
}

public class HistoryEntry
{
    public long Id { get; set; }
    public int LeadId { get; set; }
    public string FieldName { get; set; } = string.Empty;
    public string? OldValue { get; set; }
    public string? NewValue { get; set; }
    public DateTime ChangedAt { get; set; } = DateTime.UtcNow;
    public string ChangedBy { get; set; } = string.Empty;
    public string? Metadata { get; set; }
    public string Tier { get; set; } = "Hot";
}

public class CreateLeadRequest
{
    public string Name { get; set; } = string.Empty;
    public string ContactNo { get; set; } = string.Empty;
    public string? AlternateContactNo { get; set; }
    public string? Email { get; set; }
    public string? Notes { get; set; }
    public DateTime? ScheduledDate { get; set; }
    public string? ChosenProject { get; set; }
    public Guid AssignTo { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

// DbContext
public class CrmDbContext : DbContext
{
    public CrmDbContext(DbContextOptions<CrmDbContext> options) : base(options) { }
    
    public DbSet<Lead> Leads { get; set; } = null!;
    public DbSet<HistoryEntry> HistoryEntries { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<Lead>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).HasMaxLength(200);
            entity.Property(e => e.ContactNo).HasMaxLength(20).IsRequired();
            entity.Property(e => e.AlternateContactNo).HasMaxLength(20);
            entity.Property(e => e.Email).HasMaxLength(255);
            entity.Property(e => e.Notes).HasMaxLength(2000);
            entity.Property(e => e.ChosenProject).HasMaxLength(200);
            entity.Property(e => e.CreatedBy).HasMaxLength(100).IsRequired();
            entity.Property(e => e.UpdatedBy).HasMaxLength(100).IsRequired();
        });

        modelBuilder.Entity<HistoryEntry>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.FieldName).HasMaxLength(100).IsRequired();
            entity.Property(e => e.OldValue).HasMaxLength(4000);
            entity.Property(e => e.NewValue).HasMaxLength(4000);
            entity.Property(e => e.ChangedBy).HasMaxLength(100).IsRequired();
            entity.Property(e => e.Metadata).HasMaxLength(2000);
            entity.Property(e => e.Tier).HasMaxLength(10).IsRequired();
        });
    }
}

// Controllers
[Microsoft.AspNetCore.Mvc.ApiController]
[Microsoft.AspNetCore.Mvc.Route("api/[controller]")]
public class LeadsController : Microsoft.AspNetCore.Mvc.ControllerBase
{
    private readonly CrmDbContext _context;
    private readonly ILogger<LeadsController> _logger;

    public LeadsController(CrmDbContext context, ILogger<LeadsController> logger)
    {
        _context = context;
        _logger = logger;
    }

    [Microsoft.AspNetCore.Mvc.HttpGet]
    public async Task<Microsoft.AspNetCore.Mvc.ActionResult<IEnumerable<Lead>>> GetLeads()
    {
        return await _context.Leads.ToListAsync();
    }

    [Microsoft.AspNetCore.Mvc.HttpGet("{id}")]
    public async Task<Microsoft.AspNetCore.Mvc.ActionResult<Lead>> GetLead(int id)
    {
        var lead = await _context.Leads.FindAsync(id);
        if (lead == null) return NotFound();
        return lead;
    }

    [Microsoft.AspNetCore.Mvc.HttpPost]
    public async Task<Microsoft.AspNetCore.Mvc.ActionResult<Lead>> CreateLead(CreateLeadRequest request)
    {
        var lead = new Lead
        {
            Name = request.Name,
            ContactNo = request.ContactNo,
            AlternateContactNo = request.AlternateContactNo,
            Email = request.Email,
            Notes = request.Notes,
            ScheduledDate = request.ScheduledDate,
            ChosenProject = request.ChosenProject,
            AssignTo = request.AssignTo,
            CreatedBy = request.CreatedBy,
            UpdatedBy = request.CreatedBy
        };

        _context.Leads.Add(lead);
        await _context.SaveChangesAsync();

        // Record creation history
        var historyEntries = new List<HistoryEntry>();
        if (!string.IsNullOrEmpty(lead.Name))
            historyEntries.Add(new HistoryEntry { LeadId = lead.Id, FieldName = "Name", OldValue = null, NewValue = lead.Name, ChangedBy = request.CreatedBy, Tier = "Hot" });
        if (!string.IsNullOrEmpty(lead.ContactNo))
            historyEntries.Add(new HistoryEntry { LeadId = lead.Id, FieldName = "ContactNo", OldValue = null, NewValue = lead.ContactNo, ChangedBy = request.CreatedBy, Tier = "Hot" });
        if (!string.IsNullOrEmpty(lead.Email))
            historyEntries.Add(new HistoryEntry { LeadId = lead.Id, FieldName = "Email", OldValue = null, NewValue = lead.Email, ChangedBy = request.CreatedBy, Tier = "Hot" });
        if (!string.IsNullOrEmpty(lead.Notes))
            historyEntries.Add(new HistoryEntry { LeadId = lead.Id, FieldName = "Notes", OldValue = null, NewValue = lead.Notes, ChangedBy = request.CreatedBy, Tier = "Hot" });
        if (!string.IsNullOrEmpty(lead.ChosenProject))
            historyEntries.Add(new HistoryEntry { LeadId = lead.Id, FieldName = "ChosenProject", OldValue = null, NewValue = lead.ChosenProject, ChangedBy = request.CreatedBy, Tier = "Hot" });

        if (historyEntries.Any())
        {
            _context.HistoryEntries.AddRange(historyEntries);
            await _context.SaveChangesAsync();
        }

        return Microsoft.AspNetCore.Mvc.CreatedAtActionResult.CreatedAtAction(nameof(GetLead), new { id = lead.Id }, lead);
    }

    [Microsoft.AspNetCore.Mvc.HttpGet("{id}/history")]
    public async Task<Microsoft.AspNetCore.Mvc.ActionResult<IEnumerable<HistoryEntry>>> GetLeadHistory(int id)
    {
        var lead = await _context.Leads.FindAsync(id);
        if (lead == null) return NotFound();

        var history = await _context.HistoryEntries
            .Where(h => h.LeadId == id)
            .OrderByDescending(h => h.ChangedAt)
            .ToListAsync();

        return history;
    }
}
