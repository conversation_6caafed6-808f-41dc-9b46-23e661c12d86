using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Core.Interfaces;

/// <summary>
/// Primary interface for CRM history storage and retrieval operations.
/// Provides high-performance access to tiered storage with caching support.
/// </summary>
public interface IHistoryService
{
    /// <summary>
    /// Retrieves history entries based on the specified query criteria.
    /// Automatically routes queries across appropriate storage tiers.
    /// </summary>
    /// <param name="query">Query criteria including filters and pagination</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Paginated result with history entries and metadata</returns>
    Task<HistoryResult<HistoryEntry>> GetHistoryAsync(
        HistoryQuery query, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a single history entry to the appropriate storage tier.
    /// Automatically determines tier based on entry timestamp.
    /// </summary>
    /// <param name="entry">History entry to add</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>True if the entry was successfully added</returns>
    Task<bool> AddHistoryEntryAsync(
        HistoryEntry entry, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds multiple history entries in a single batch operation.
    /// Optimized for high-throughput scenarios with automatic batching.
    /// </summary>
    /// <param name="entries">Collection of history entries to add</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>True if all entries were successfully added</returns>
    Task<bool> AddHistoryBatchAsync(
        IEnumerable<HistoryEntry> entries, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a validated batch of history entries.
    /// Provides detailed result information for batch processing scenarios.
    /// </summary>
    /// <param name="batch">Pre-validated batch of history entries</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Detailed result of the batch operation</returns>
    Task<BatchOperationResult> AddHistoryBatchAsync(
        HistoryBatch batch, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Archives old history entries from hot/warm tiers to cold storage.
    /// Automatically manages tier transitions based on configured retention policies.
    /// </summary>
    /// <param name="cutoffDate">Entries older than this date will be archived</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Number of entries successfully archived</returns>
    Task<int> ArchiveOldEntriesAsync(
        DateTime cutoffDate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets statistics about history storage across all tiers.
    /// Useful for monitoring and capacity planning.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Storage statistics by tier</returns>
    Task<HistoryStorageStats> GetStorageStatsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates the integrity of history data across all tiers.
    /// Checks for data consistency and identifies potential issues.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Data integrity validation result</returns>
    Task<DataIntegrityResult> ValidateDataIntegrityAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Clears cached data for the specified criteria.
    /// Useful for cache invalidation after data modifications.
    /// </summary>
    /// <param name="leadId">Optional lead ID to clear cache for</param>
    /// <param name="fieldName">Optional field name to clear cache for</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>True if cache was successfully cleared</returns>
    Task<bool> ClearCacheAsync(
        int? leadId = null, 
        string? fieldName = null, 
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for caching history data to improve query performance.
/// </summary>
public interface IHistoryCache
{
    /// <summary>
    /// Gets cached query result if available.
    /// </summary>
    Task<HistoryResult<HistoryEntry>?> GetAsync(
        string cacheKey, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Stores query result in cache with specified expiration.
    /// </summary>
    Task SetAsync(
        string cacheKey, 
        HistoryResult<HistoryEntry> result, 
        TimeSpan expiration, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes cached data for the specified key pattern.
    /// </summary>
    Task RemoveAsync(
        string keyPattern, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a cache key for the specified query.
    /// </summary>
    string GenerateCacheKey(HistoryQuery query);
}

/// <summary>
/// Interface for tier-specific storage operations.
/// </summary>
public interface ITierStorage
{
    /// <summary>
    /// The storage tier this implementation handles.
    /// </summary>
    StorageTier Tier { get; }

    /// <summary>
    /// Queries history entries from this specific tier.
    /// </summary>
    Task<HistoryResult<HistoryEntry>> QueryAsync(
        HistoryQuery query, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds history entries to this specific tier.
    /// </summary>
    Task<bool> AddEntriesAsync(
        IEnumerable<HistoryEntry> entries, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Moves entries from this tier to a different tier.
    /// </summary>
    Task<int> MoveEntriesAsync(
        DateTime cutoffDate, 
        StorageTier targetTier, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets storage statistics for this tier.
    /// </summary>
    Task<TierStorageStats> GetStatsAsync(
        CancellationToken cancellationToken = default);
}
