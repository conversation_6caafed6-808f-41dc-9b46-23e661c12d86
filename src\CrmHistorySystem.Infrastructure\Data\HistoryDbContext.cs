using Microsoft.EntityFrameworkCore;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Infrastructure.Data.Configurations;

namespace CrmHistorySystem.Infrastructure.Data;

/// <summary>
/// Entity Framework context for history storage in SQL Server (Hot and Warm tiers).
/// Optimized for high-performance queries with proper indexing and partitioning.
/// </summary>
public class HistoryDbContext : DbContext
{
    /// <summary>
    /// The storage tier this context represents.
    /// </summary>
    public StorageTier Tier { get; }

    /// <summary>
    /// History entries table.
    /// </summary>
    public DbSet<HistoryEntry> HistoryEntries { get; set; } = null!;

    public HistoryDbContext(DbContextOptions<HistoryDbContext> options, StorageTier tier) 
        : base(options)
    {
        Tier = tier;
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply entity configurations
        modelBuilder.ApplyConfiguration(new HistoryEntryConfiguration(Tier));

        // Configure table name based on tier
        var tableName = Tier switch
        {
            StorageTier.Hot => "HistoryEntries_Hot",
            StorageTier.Warm => "HistoryEntries_Warm",
            _ => "HistoryEntries"
        };

        modelBuilder.Entity<HistoryEntry>().ToTable(tableName);

        // Add tier-specific optimizations
        ConfigureTierSpecificOptimizations(modelBuilder);
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);

        // Enable sensitive data logging in development
        if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
        {
            optionsBuilder.EnableSensitiveDataLogging();
        }

        // Configure query tracking behavior for performance
        optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);

        // Enable retry on failure for resilience
        optionsBuilder.EnableRetryOnFailure(
            maxRetryCount: 3,
            maxRetryDelay: TimeSpan.FromSeconds(5),
            errorNumbersToAdd: null);
    }

    private void ConfigureTierSpecificOptimizations(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<HistoryEntry>();

        switch (Tier)
        {
            case StorageTier.Hot:
                // Hot tier optimizations for maximum performance
                entity.HasIndex(e => new { e.LeadId, e.ChangedAt })
                      .HasDatabaseName("IX_HistoryEntries_Hot_LeadId_ChangedAt")
                      .IsDescending(false, true); // LeadId ASC, ChangedAt DESC

                entity.HasIndex(e => new { e.FieldName, e.ChangedAt })
                      .HasDatabaseName("IX_HistoryEntries_Hot_FieldName_ChangedAt")
                      .IsDescending(false, true);

                entity.HasIndex(e => e.ChangedAt)
                      .HasDatabaseName("IX_HistoryEntries_Hot_ChangedAt")
                      .IsDescending(true);

                entity.HasIndex(e => new { e.ChangedBy, e.ChangedAt })
                      .HasDatabaseName("IX_HistoryEntries_Hot_ChangedBy_ChangedAt")
                      .IsDescending(false, true);

                break;

            case StorageTier.Warm:
                // Warm tier optimizations with compression
                entity.HasIndex(e => new { e.LeadId, e.ChangedAt })
                      .HasDatabaseName("IX_HistoryEntries_Warm_LeadId_ChangedAt")
                      .IsDescending(false, true);

                entity.HasIndex(e => e.ChangedAt)
                      .HasDatabaseName("IX_HistoryEntries_Warm_ChangedAt")
                      .IsDescending(true);

                // Use page compression for warm tier to save space
                entity.HasAnnotation("SqlServer:DataCompression", "Page");
                break;
        }
    }

    /// <summary>
    /// Bulk insert history entries for optimal performance.
    /// </summary>
    public async Task<int> BulkInsertHistoryEntriesAsync(
        IEnumerable<HistoryEntry> entries, 
        CancellationToken cancellationToken = default)
    {
        var entryList = entries.ToList();
        if (!entryList.Any())
            return 0;

        // Use AddRange for bulk operations
        await HistoryEntries.AddRangeAsync(entryList, cancellationToken);
        return await SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Executes a raw SQL query for complex operations.
    /// </summary>
    public async Task<int> ExecuteRawSqlAsync(
        string sql, 
        params object[] parameters)
    {
        return await Database.ExecuteSqlRawAsync(sql, parameters);
    }

    /// <summary>
    /// Gets database statistics for monitoring.
    /// </summary>
    public async Task<TierStorageStats> GetStorageStatsAsync(CancellationToken cancellationToken = default)
    {
        var tableName = Tier switch
        {
            StorageTier.Hot => "HistoryEntries_Hot",
            StorageTier.Warm => "HistoryEntries_Warm",
            _ => "HistoryEntries"
        };

        // Get basic statistics
        var entryCount = await HistoryEntries.LongCountAsync(cancellationToken);
        
        var oldestEntry = await HistoryEntries
            .OrderBy(e => e.ChangedAt)
            .Select(e => e.ChangedAt)
            .FirstOrDefaultAsync(cancellationToken);

        var newestEntry = await HistoryEntries
            .OrderByDescending(e => e.ChangedAt)
            .Select(e => e.ChangedAt)
            .FirstOrDefaultAsync(cancellationToken);

        // Get table size using raw SQL
        var sizeQuery = $@"
            SELECT 
                SUM(a.total_pages) * 8 * 1024 as SizeBytes
            FROM sys.tables t
            INNER JOIN sys.indexes i ON t.object_id = i.object_id
            INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
            INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
            WHERE t.name = '{tableName}'";

        var sizeBytes = 0L;
        try
        {
            var connection = Database.GetDbConnection();
            await connection.OpenAsync(cancellationToken);
            
            using var command = connection.CreateCommand();
            command.CommandText = sizeQuery;
            var result = await command.ExecuteScalarAsync(cancellationToken);
            sizeBytes = result != null ? Convert.ToInt64(result) : 0;
        }
        catch
        {
            // If we can't get the exact size, estimate it
            sizeBytes = entryCount * 500; // Rough estimate of 500 bytes per entry
        }

        return new TierStorageStats
        {
            Tier = Tier,
            EntryCount = entryCount,
            SizeBytes = sizeBytes,
            OldestEntry = oldestEntry == default ? null : oldestEntry,
            NewestEntry = newestEntry == default ? null : newestEntry,
            AverageQueryTimeMs = GetAverageQueryTime(),
            EstimatedMonthlyCost = CalculateEstimatedCost(sizeBytes)
        };
    }

    private double GetAverageQueryTime()
    {
        // This would typically come from performance monitoring
        // For now, return tier-based estimates
        return Tier switch
        {
            StorageTier.Hot => 25.0,
            StorageTier.Warm => 75.0,
            _ => 100.0
        };
    }

    private decimal CalculateEstimatedCost(long sizeBytes)
    {
        // Rough cost estimates based on Azure SQL pricing
        var sizeGB = sizeBytes / (1024.0 * 1024.0 * 1024.0);
        
        return Tier switch
        {
            StorageTier.Hot => (decimal)(sizeGB * 10.0), // $10/GB/month for premium SSD
            StorageTier.Warm => (decimal)(sizeGB * 3.0), // $3/GB/month for standard storage
            _ => (decimal)(sizeGB * 1.0)
        };
    }
}
