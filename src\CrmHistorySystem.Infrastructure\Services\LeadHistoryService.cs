using Microsoft.Extensions.Logging;
using System.Reflection;
using System.Text.Json;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Core.Services;

namespace CrmHistorySystem.Infrastructure.Services;

/// <summary>
/// Implementation of lead-specific history operations using the tiered history system.
/// Provides high-level operations for tracking lead changes with automatic property reflection.
/// </summary>
public class LeadHistoryService : ILeadHistoryService
{
    private readonly IHistoryService _historyService;
    private readonly ILogger<LeadHistoryService> _logger;

    // Properties to exclude from automatic history tracking
    private static readonly HashSet<string> ExcludedProperties = new(StringComparer.OrdinalIgnoreCase)
    {
        "Id", "CreatedAt", "UpdatedAt", "Version", "RowVersion", "Timestamp"
    };

    public LeadHistoryService(
        IHistoryService historyService,
        ILogger<LeadHistoryService> logger)
    {
        _historyService = historyService;
        _logger = logger;
    }

    /// <summary>
    /// Records the initial creation of a lead with all non-null property values.
    /// </summary>
    public async Task<bool> RecordLeadCreationAsync<T>(T lead, string createdBy, CancellationToken cancellationToken = default) 
        where T : class
    {
        try
        {
            var leadId = GetLeadId(lead);
            if (leadId <= 0)
            {
                _logger.LogWarning("Invalid lead ID for creation history: {LeadId}", leadId);
                return false;
            }

            var changes = ExtractCreationChanges(lead);
            if (!changes.Any())
            {
                _logger.LogDebug("No properties with values found for lead creation history: {LeadId}", leadId);
                return true; // Not an error, just nothing to record
            }

            var result = await RecordBatchChangesAsync(leadId, changes, createdBy, 
                $"{{\"operation\": \"create\", \"entityType\": \"{typeof(T).Name}\"}}", cancellationToken);

            _logger.LogDebug("Recorded creation history for lead {LeadId}: {SuccessCount}/{TotalCount} entries", 
                leadId, result.SuccessCount, result.TotalCount);

            return result.IsSuccess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording lead creation history for {LeadType}", typeof(T).Name);
            return false;
        }
    }

    /// <summary>
    /// Records changes to a lead by comparing old and new values.
    /// </summary>
    public async Task<bool> RecordLeadChangesAsync<T>(int leadId, T oldLead, T newLead, string modifiedBy, CancellationToken cancellationToken = default) 
        where T : class
    {
        try
        {
            var changes = ExtractModificationChanges(oldLead, newLead);
            if (!changes.Any())
            {
                _logger.LogDebug("No changes detected for lead {LeadId}", leadId);
                return true; // Not an error, just no changes
            }

            var result = await RecordBatchChangesAsync(leadId, changes, modifiedBy, 
                $"{{\"operation\": \"update\", \"entityType\": \"{typeof(T).Name}\", \"changesCount\": {changes.Count()}}}", 
                cancellationToken);

            _logger.LogDebug("Recorded modification history for lead {LeadId}: {SuccessCount}/{TotalCount} entries", 
                leadId, result.SuccessCount, result.TotalCount);

            return result.IsSuccess;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording lead modification history for lead {LeadId}", leadId);
            return false;
        }
    }

    /// <summary>
    /// Records a specific field change for a lead.
    /// </summary>
    public async Task<bool> RecordFieldChangeAsync(
        int leadId, 
        string fieldName, 
        string? oldValue, 
        string? newValue, 
        string changedBy, 
        string? metadata = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var entry = new HistoryEntry
            {
                LeadId = leadId,
                FieldName = fieldName,
                OldValue = oldValue,
                NewValue = newValue,
                ChangedAt = DateTime.UtcNow,
                ChangedBy = changedBy,
                Metadata = metadata
            };

            var success = await _historyService.AddHistoryEntryAsync(entry, cancellationToken);
            
            if (success)
            {
                _logger.LogDebug("Recorded field change for lead {LeadId}: {FieldName} = '{OldValue}' → '{NewValue}'", 
                    leadId, fieldName, oldValue, newValue);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording field change for lead {LeadId}, field {FieldName}", leadId, fieldName);
            return false;
        }
    }

    /// <summary>
    /// Records multiple field changes for a lead in a single batch operation.
    /// </summary>
    public async Task<BatchOperationResult> RecordBatchChangesAsync(
        int leadId, 
        IEnumerable<FieldChange> changes, 
        string changedBy, 
        string? metadata = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var changesList = changes.ToList();
            if (!changesList.Any())
            {
                return BatchOperationResult.Success(Guid.NewGuid(), 0, 0);
            }

            var entries = changesList.Select(change => new HistoryEntry
            {
                LeadId = leadId,
                FieldName = change.FieldName,
                OldValue = change.OldValue,
                NewValue = change.NewValue,
                ChangedAt = DateTime.UtcNow,
                ChangedBy = changedBy,
                Metadata = change.Metadata ?? metadata
            }).ToList();

            var batch = HistoryBatch.Create(entries, changedBy, metadata ?? "Lead field changes");
            var result = await _historyService.AddHistoryBatchAsync(batch, cancellationToken);

            _logger.LogDebug("Recorded batch changes for lead {LeadId}: {SuccessCount}/{TotalCount} entries", 
                leadId, result.SuccessCount, result.TotalCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording batch changes for lead {LeadId}", leadId);
            return BatchOperationResult.Failure(Guid.NewGuid(), changes.Count(), ex.Message);
        }
    }

    /// <summary>
    /// Retrieves history for a specific lead.
    /// </summary>
    public async Task<HistoryResult<HistoryEntry>> GetLeadHistoryAsync(
        int leadId,
        int page = 1,
        int pageSize = 50,
        string? fieldName = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new HistoryQuery
            {
                LeadId = leadId,
                FieldName = fieldName,
                StartDate = startDate,
                EndDate = endDate,
                Page = page,
                PageSize = pageSize,
                SortOrder = HistorySortOrder.ChangedAtDescending
            };

            return await _historyService.GetHistoryAsync(query, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving history for lead {LeadId}", leadId);
            throw;
        }
    }

    /// <summary>
    /// Extracts field changes for lead creation (all non-null properties).
    /// </summary>
    private List<FieldChange> ExtractCreationChanges<T>(T lead) where T : class
    {
        var changes = new List<FieldChange>();
        var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            if (ShouldExcludeProperty(property))
                continue;

            var value = property.GetValue(lead);
            if (value != null)
            {
                var stringValue = ConvertToString(value);
                if (!string.IsNullOrEmpty(stringValue))
                {
                    changes.Add(FieldChange.ForCreation(property.Name, stringValue));
                }
            }
        }

        return changes;
    }

    /// <summary>
    /// Extracts field changes by comparing old and new lead objects.
    /// </summary>
    private List<FieldChange> ExtractModificationChanges<T>(T oldLead, T newLead) where T : class
    {
        var changes = new List<FieldChange>();
        var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            if (ShouldExcludeProperty(property))
                continue;

            var oldValue = property.GetValue(oldLead);
            var newValue = property.GetValue(newLead);

            var oldStringValue = ConvertToString(oldValue);
            var newStringValue = ConvertToString(newValue);

            if (!string.Equals(oldStringValue, newStringValue, StringComparison.Ordinal))
            {
                changes.Add(FieldChange.ForModification(property.Name, oldStringValue, newStringValue));
            }
        }

        return changes;
    }

    /// <summary>
    /// Determines if a property should be excluded from history tracking.
    /// </summary>
    private bool ShouldExcludeProperty(PropertyInfo property)
    {
        // Exclude properties that are not readable
        if (!property.CanRead)
            return true;

        // Exclude properties in the exclusion list
        if (ExcludedProperties.Contains(property.Name))
            return true;

        // Exclude complex types that are not easily serializable
        var propertyType = property.PropertyType;
        if (propertyType.IsClass && propertyType != typeof(string) && !propertyType.IsEnum)
        {
            // Allow nullable types
            var underlyingType = Nullable.GetUnderlyingType(propertyType);
            if (underlyingType == null)
                return true;
        }

        return false;
    }

    /// <summary>
    /// Converts a property value to its string representation for storage.
    /// </summary>
    private string? ConvertToString(object? value)
    {
        if (value == null)
            return null;

        return value switch
        {
            string str => str,
            DateTime dt => dt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            DateTimeOffset dto => dto.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            Guid guid => guid.ToString(),
            bool boolean => boolean.ToString().ToLowerInvariant(),
            _ => value.ToString()
        };
    }

    /// <summary>
    /// Extracts the lead ID from a lead object using reflection.
    /// </summary>
    private int GetLeadId<T>(T lead) where T : class
    {
        var idProperty = typeof(T).GetProperty("Id") ?? typeof(T).GetProperty("LeadId");
        if (idProperty?.CanRead == true)
        {
            var value = idProperty.GetValue(lead);
            if (value is int intValue)
                return intValue;
        }

        _logger.LogWarning("Could not extract lead ID from {LeadType}", typeof(T).Name);
        return 0;
    }
}
