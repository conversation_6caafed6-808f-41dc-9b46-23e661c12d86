using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Core.Configuration;
using CrmHistorySystem.Core.Extensions;

namespace CrmHistorySystem.Infrastructure.Caching;

/// <summary>
/// Redis-based implementation of history caching for high-performance data access.
/// Provides distributed caching with automatic expiration and cache invalidation.
/// </summary>
public class RedisHistoryCache : IHistoryCache
{
    private readonly IDistributedCache _distributedCache;
    private readonly ILogger<RedisHistoryCache> _logger;
    private readonly HistoryOptions _options;
    private readonly JsonSerializerOptions _jsonOptions;

    public RedisHistoryCache(
        IDistributedCache distributedCache,
        ILogger<RedisHistoryCache> logger,
        IOptions<HistoryOptions> options)
    {
        _distributedCache = distributedCache;
        _logger = logger;
        _options = options.Value;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };
    }

    /// <summary>
    /// Gets cached query result if available.
    /// </summary>
    public async Task<HistoryResult<HistoryEntry>?> GetAsync(
        string cacheKey, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cachedData = await _distributedCache.GetStringAsync(cacheKey, cancellationToken);
            
            if (string.IsNullOrEmpty(cachedData))
            {
                _logger.LogDebug("Cache miss for key: {CacheKey}", cacheKey);
                return null;
            }

            var result = JsonSerializer.Deserialize<CachedHistoryResult>(cachedData, _jsonOptions);
            if (result == null)
            {
                _logger.LogWarning("Failed to deserialize cached data for key: {CacheKey}", cacheKey);
                return null;
            }

            // Check if cache entry has expired
            if (result.ExpiresAt <= DateTime.UtcNow)
            {
                _logger.LogDebug("Cache entry expired for key: {CacheKey}", cacheKey);
                await RemoveAsync(cacheKey, cancellationToken);
                return null;
            }

            _logger.LogDebug("Cache hit for key: {CacheKey}, expires at: {ExpiresAt}", 
                cacheKey, result.ExpiresAt);

            return HistoryResult<HistoryEntry>.FromCache(
                result.Data,
                result.TotalCount,
                result.CurrentPage,
                result.PageSize,
                result.ExpiresAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving data from cache for key: {CacheKey}", cacheKey);
            return null;
        }
    }

    /// <summary>
    /// Stores query result in cache with specified expiration.
    /// </summary>
    public async Task SetAsync(
        string cacheKey, 
        HistoryResult<HistoryEntry> result, 
        TimeSpan expiration, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var expiresAt = DateTime.UtcNow.Add(expiration);
            var cachedResult = new CachedHistoryResult
            {
                Data = result.Data,
                TotalCount = result.TotalCount,
                CurrentPage = result.CurrentPage,
                PageSize = result.PageSize,
                ExpiresAt = expiresAt
            };

            var serializedData = JsonSerializer.Serialize(cachedResult, _jsonOptions);
            
            var cacheOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration,
                SlidingExpiration = TimeSpan.FromMinutes(Math.Min(expiration.TotalMinutes / 2, 30))
            };

            await _distributedCache.SetStringAsync(cacheKey, serializedData, cacheOptions, cancellationToken);
            
            _logger.LogDebug("Cached result for key: {CacheKey}, expires at: {ExpiresAt}, size: {Size} bytes", 
                cacheKey, expiresAt, serializedData.Length);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error storing data in cache for key: {CacheKey}", cacheKey);
        }
    }

    /// <summary>
    /// Removes cached data for the specified key pattern.
    /// </summary>
    public async Task RemoveAsync(string keyPattern, CancellationToken cancellationToken = default)
    {
        try
        {
            if (keyPattern.Contains('*') || keyPattern.Contains('?'))
            {
                // Pattern-based removal (requires Redis-specific implementation)
                await RemoveByPatternAsync(keyPattern, cancellationToken);
            }
            else
            {
                // Single key removal
                await _distributedCache.RemoveAsync(keyPattern, cancellationToken);
                _logger.LogDebug("Removed cache entry for key: {CacheKey}", keyPattern);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing cache entry for pattern: {KeyPattern}", keyPattern);
        }
    }

    /// <summary>
    /// Generates a cache key for the specified query.
    /// </summary>
    public string GenerateCacheKey(HistoryQuery query)
    {
        var baseKey = query.ToCacheKey(_options.Cache.KeyPrefix);
        
        // Add tier information to the key for proper cache segmentation
        var requiredTiers = query.GetRequiredTiers();
        var tierSuffix = string.Join(",", requiredTiers.Select(t => t.ToString().ToLower()));
        
        return $"{baseKey}:tiers:{tierSuffix}";
    }

    /// <summary>
    /// Clears cache entries for a specific lead.
    /// </summary>
    public async Task ClearLeadCacheAsync(int leadId, CancellationToken cancellationToken = default)
    {
        var pattern = $"{_options.Cache.KeyPrefix}*lead:{leadId}*";
        await RemoveByPatternAsync(pattern, cancellationToken);
        
        _logger.LogDebug("Cleared cache entries for lead: {LeadId}", leadId);
    }

    /// <summary>
    /// Clears cache entries for a specific field.
    /// </summary>
    public async Task ClearFieldCacheAsync(string fieldName, CancellationToken cancellationToken = default)
    {
        var pattern = $"{_options.Cache.KeyPrefix}*field:{fieldName}*";
        await RemoveByPatternAsync(pattern, cancellationToken);
        
        _logger.LogDebug("Cleared cache entries for field: {FieldName}", fieldName);
    }

    /// <summary>
    /// Gets cache statistics for monitoring.
    /// </summary>
    public async Task<CacheStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        // This would require Redis-specific implementation to get actual statistics
        // For now, return basic statistics
        return new CacheStatistics
        {
            HitRate = 0.85, // Would be calculated from actual metrics
            MissRate = 0.15,
            TotalKeys = 0, // Would query Redis for actual count
            TotalMemoryUsage = 0,
            AverageKeySize = 0
        };
    }

    private async Task RemoveByPatternAsync(string pattern, CancellationToken cancellationToken)
    {
        // This requires Redis-specific implementation using StackExchange.Redis
        // For now, we'll log the pattern that would be removed
        _logger.LogDebug("Would remove cache entries matching pattern: {Pattern}", pattern);
        
        // In a real implementation, you would:
        // 1. Get IConnectionMultiplexer from DI
        // 2. Use SCAN command to find matching keys
        // 3. Use DEL command to remove them in batches
        
        await Task.CompletedTask;
    }
}

/// <summary>
/// Internal model for cached history results.
/// </summary>
internal class CachedHistoryResult
{
    public IEnumerable<HistoryEntry> Data { get; set; } = Enumerable.Empty<HistoryEntry>();
    public int TotalCount { get; set; }
    public int CurrentPage { get; set; }
    public int PageSize { get; set; }
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// Cache performance statistics.
/// </summary>
public class CacheStatistics
{
    public double HitRate { get; set; }
    public double MissRate { get; set; }
    public long TotalKeys { get; set; }
    public long TotalMemoryUsage { get; set; }
    public double AverageKeySize { get; set; }
    
    public override string ToString()
    {
        return $"Cache Stats: {HitRate:P1} hit rate, {TotalKeys:N0} keys, {TotalMemoryUsage / (1024 * 1024):N1} MB";
    }
}
