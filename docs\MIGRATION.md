# CRM History System - Migration Guide

This guide provides step-by-step instructions for migrating from the existing JSON-based history storage to the new high-performance tiered storage system.

## 📋 Migration Overview

### Current State
- History data stored as JSON in a single table
- Performance issues with large datasets
- High storage costs
- Limited query capabilities

### Target State
- Tiered storage architecture (Hot/Warm/Cold)
- Relational data structure optimized for queries
- 80-90% cost reduction
- Sub-100ms query performance for recent data

### Migration Strategy
- **Zero-downtime migration** using blue-green deployment
- **Incremental data migration** in batches
- **Automatic rollback capability** if issues occur
- **Data validation** at every step

## 🎯 Migration Phases

### Phase 1: Pre-Migration Preparation (1-2 weeks)

#### 1.1 Environment Setup
```bash
# Deploy new infrastructure
az deployment group create \
  --resource-group rg-crm-history \
  --template-file azure/main.bicep \
  --parameters environment=staging

# Verify all services are running
curl -f https://crm-history-staging.azurewebsites.net/health
```

#### 1.2 Data Analysis
```sql
-- Analyze current data volume and distribution
SELECT 
    YEAR(JSON_VALUE(HistoryData, '$.changedAt')) as Year,
    MONTH(JSON_VALUE(HistoryData, '$.changedAt')) as Month,
    COUNT(*) as RecordCount,
    SUM(DATALENGTH(HistoryData)) / 1024 / 1024 as SizeMB
FROM LegacyHistoryTable
GROUP BY 
    YEAR(JSON_VALUE(HistoryData, '$.changedAt')),
    MONTH(JSON_VALUE(HistoryData, '$.changedAt'))
ORDER BY Year DESC, Month DESC;

-- Identify data quality issues
SELECT 
    COUNT(*) as TotalRecords,
    COUNT(CASE WHEN JSON_VALUE(HistoryData, '$.leadId') IS NULL THEN 1 END) as MissingLeadId,
    COUNT(CASE WHEN JSON_VALUE(HistoryData, '$.fieldName') IS NULL THEN 1 END) as MissingFieldName,
    COUNT(CASE WHEN JSON_VALUE(HistoryData, '$.changedBy') IS NULL THEN 1 END) as MissingChangedBy
FROM LegacyHistoryTable;
```

#### 1.3 Performance Baseline
```bash
# Run performance tests on current system
cd tests/CrmHistorySystem.Tests
dotnet test --filter "Category=Baseline" --logger "console;verbosity=detailed"
```

### Phase 2: Schema Migration (1 week)

#### 2.1 Create New Database Schema
```sql
-- Execute schema creation scripts
-- Run: src/CrmHistorySystem.Infrastructure/Migrations/001_InitialCreate.sql
-- Run: src/CrmHistorySystem.Infrastructure/Migrations/002_DataMigration.sql
-- Run: src/CrmHistorySystem.Infrastructure/Migrations/003_RollbackProcedures.sql

-- Verify schema creation
SELECT 
    TABLE_NAME,
    TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'dbo' 
AND TABLE_NAME LIKE 'HistoryEntries_%';
```

#### 2.2 Create Backup
```sql
-- Create full backup before migration
EXEC sp_BackupBeforeMigration 
    @BackupTableSuffix = 'PreMigration_Production',
    @CreateBackupTables = 1,
    @BackupExistingData = 1;
```

### Phase 3: Data Migration (2-3 weeks)

#### 3.1 Dry Run Migration
```sql
-- Test migration with a subset of data
EXEC sp_MigrateHistoryData 
    @SourceTable = 'LegacyHistoryTable',
    @StartFromId = 0,
    @BatchSize = 1000,
    @MaxBatches = 10,
    @DryRun = 1;
```

#### 3.2 Production Migration
```sql
-- Execute full migration in batches
EXEC sp_MigrateHistoryData 
    @SourceTable = 'LegacyHistoryTable',
    @StartFromId = 0,
    @BatchSize = 10000,
    @MaxBatches = 1000,
    @DryRun = 0;
```

#### 3.3 Monitor Migration Progress
```sql
-- Check migration status
SELECT 
    MigrationName,
    TotalRecords,
    ProcessedRecords,
    (ProcessedRecords * 100.0 / TotalRecords) as ProgressPercent,
    Status,
    LastUpdateTime
FROM HistoryMigrationState
ORDER BY StartTime DESC;

-- Check batch details
SELECT 
    BatchNumber,
    RecordsProcessed,
    RecordsSuccessful,
    RecordsFailed,
    Status,
    DATEDIFF(SECOND, StartTime, EndTime) as DurationSeconds
FROM HistoryMigrationLog
ORDER BY BatchNumber DESC;
```

### Phase 4: Validation and Testing (1 week)

#### 4.1 Data Validation
```sql
-- Validate migrated data
EXEC sp_ValidateMigratedData @SourceTable = 'LegacyHistoryTable';

-- Compare record counts
SELECT 'Source' as TableType, COUNT(*) as RecordCount FROM LegacyHistoryTable
UNION ALL
SELECT 'Hot Tier', COUNT(*) FROM HistoryEntries_Hot
UNION ALL
SELECT 'Warm Tier', COUNT(*) FROM HistoryEntries_Warm
UNION ALL
SELECT 'Total Target', COUNT(*) FROM vw_HistoryEntries_All;
```

#### 4.2 Performance Testing
```bash
# Run comprehensive performance tests
dotnet test --filter "Category=Performance" --logger "trx;LogFileName=migration-performance.trx"

# Load testing with realistic data
cd tests/CrmHistorySystem.Tests
dotnet run --project PerformanceTests -- --scenario migration-validation --duration 300s
```

#### 4.3 Application Testing
```bash
# Test API endpoints with migrated data
curl -X GET "https://api.company.com/api/v1/history?leadId=12345&pageSize=50"
curl -X GET "https://api.company.com/api/v1/history/stats"

# Test batch operations
curl -X POST "https://api.company.com/api/v1/history/batch" \
  -H "Content-Type: application/json" \
  -d @test-batch.json
```

### Phase 5: Go-Live (1 week)

#### 5.1 Blue-Green Deployment
```bash
# Deploy to production slot
az webapp deployment slot create \
  --name crm-history-app-prod \
  --slot green

# Deploy new version to green slot
az webapp deployment source config \
  --name crm-history-app-prod \
  --slot green \
  --repo-url <new-version-repo>

# Warm up green slot
curl -f https://crm-history-app-prod-green.azurewebsites.net/health
```

#### 5.2 Traffic Switching
```bash
# Switch 10% of traffic to new system
az webapp traffic-routing set \
  --name crm-history-app-prod \
  --distribution green=10

# Monitor for 24 hours, then increase gradually
az webapp traffic-routing set \
  --name crm-history-app-prod \
  --distribution green=50

# Full cutover
az webapp deployment slot swap \
  --name crm-history-app-prod \
  --slot green \
  --target-slot production
```

#### 5.3 Legacy System Decommission
```sql
-- After successful migration and validation
-- Rename legacy table for safety
EXEC sp_rename 'LegacyHistoryTable', 'LegacyHistoryTable_Archived';

-- Create view for backward compatibility (temporary)
CREATE VIEW LegacyHistoryTable AS
SELECT 
    Id,
    JSON_QUERY((
        SELECT 
            LeadId as leadId,
            FieldName as fieldName,
            OldValue as oldValue,
            NewValue as newValue,
            FORMAT(ChangedAt, 'yyyy-MM-ddTHH:mm:ss.fffZ') as changedAt,
            ChangedBy as changedBy,
            Metadata as metadata
        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
    )) as HistoryData
FROM vw_HistoryEntries_All;
```

## 🔄 Rollback Procedures

### Immediate Rollback (< 24 hours)
```bash
# Rollback application deployment
az webapp deployment slot swap \
  --name crm-history-app-prod \
  --slot production \
  --target-slot green

# Verify rollback
curl -f https://crm-history-app-prod.azurewebsites.net/health
```

### Data Rollback (if data issues discovered)
```sql
-- Rollback to pre-migration state
EXEC sp_RollbackMigration 
    @BackupName = 'PreMigration_Production',
    @RestoreData = 1,
    @ConfirmRollback = 'YES';

-- Verify rollback
SELECT COUNT(*) FROM LegacyHistoryTable;
SELECT COUNT(*) FROM HistoryEntries_Hot; -- Should be 0 after rollback
```

## 📊 Migration Metrics and KPIs

### Success Criteria
- ✅ Zero data loss (100% data integrity)
- ✅ < 1 hour total downtime
- ✅ Query performance improvement (>80% faster)
- ✅ Storage cost reduction (>80% savings)
- ✅ All existing APIs continue to work

### Monitoring Dashboard
```sql
-- Create migration monitoring view
CREATE VIEW vw_MigrationMetrics AS
SELECT 
    'Data Integrity' as Metric,
    CASE 
        WHEN (SELECT COUNT(*) FROM LegacyHistoryTable) = 
             (SELECT COUNT(*) FROM vw_HistoryEntries_All)
        THEN 'PASS' 
        ELSE 'FAIL' 
    END as Status,
    CONCAT(
        (SELECT COUNT(*) FROM vw_HistoryEntries_All), ' / ',
        (SELECT COUNT(*) FROM LegacyHistoryTable)
    ) as Details

UNION ALL

SELECT 
    'Performance',
    CASE WHEN AVG(ExecutionTimeMs) < 100 THEN 'PASS' ELSE 'FAIL' END,
    CONCAT('Avg: ', AVG(ExecutionTimeMs), 'ms')
FROM PerformanceMetrics 
WHERE MetricDate >= DATEADD(day, -1, GETDATE());
```

## 🚨 Risk Mitigation

### High-Risk Scenarios

#### 1. Data Corruption During Migration
**Mitigation:**
- Comprehensive backups before migration
- Batch processing with validation
- Automatic rollback on validation failure

#### 2. Performance Degradation
**Mitigation:**
- Extensive performance testing
- Gradual traffic switching
- Real-time monitoring with alerts

#### 3. Application Compatibility Issues
**Mitigation:**
- Backward compatibility layer
- Comprehensive API testing
- Feature flags for gradual rollout

### Contingency Plans

#### Plan A: Immediate Rollback
- Trigger: Critical issues within first 24 hours
- Action: Swap deployment slots, restore from backup
- Timeline: < 30 minutes

#### Plan B: Partial Rollback
- Trigger: Performance issues affecting specific features
- Action: Route specific queries to legacy system
- Timeline: < 2 hours

#### Plan C: Full Rollback
- Trigger: Fundamental issues requiring complete revert
- Action: Full data and application rollback
- Timeline: < 4 hours

## 📋 Migration Checklist

### Pre-Migration
- [ ] Infrastructure deployed and tested
- [ ] Database schema created
- [ ] Migration scripts tested
- [ ] Backup procedures verified
- [ ] Performance baseline established
- [ ] Rollback procedures tested

### During Migration
- [ ] Migration started with monitoring
- [ ] Batch progress tracked
- [ ] Data validation performed
- [ ] Performance metrics collected
- [ ] Error handling verified

### Post-Migration
- [ ] Data integrity validated
- [ ] Performance benchmarks met
- [ ] All APIs tested
- [ ] Monitoring configured
- [ ] Documentation updated
- [ ] Team training completed

### Go-Live
- [ ] Blue-green deployment executed
- [ ] Traffic gradually switched
- [ ] Monitoring alerts configured
- [ ] Support team notified
- [ ] Legacy system archived

## 📞 Support and Escalation

### Migration Team Contacts
- **Migration Lead**: <EMAIL>
- **Database Admin**: <EMAIL>
- **DevOps Engineer**: <EMAIL>
- **QA Lead**: <EMAIL>

### Escalation Matrix
1. **Level 1**: Development Team (Response: 15 minutes)
2. **Level 2**: Senior Engineers (Response: 30 minutes)
3. **Level 3**: Architecture Team (Response: 1 hour)
4. **Level 4**: CTO/VP Engineering (Response: 2 hours)

### Emergency Procedures
```bash
# Emergency rollback command
./scripts/emergency-rollback.sh --confirm-rollback

# Emergency contact
echo "CRITICAL: CRM History Migration Issue" | \
  mail -s "URGENT: Migration Emergency" <EMAIL>
```

## 📈 Post-Migration Optimization

### Week 1: Monitoring and Tuning
- Monitor query performance
- Adjust cache settings
- Optimize database indexes
- Fine-tune batch sizes

### Week 2-4: Performance Optimization
- Analyze slow queries
- Implement additional caching
- Optimize tier distribution
- Adjust archival policies

### Month 2-3: Cost Optimization
- Review storage tier usage
- Optimize cold storage compression
- Adjust retention policies
- Implement automated cleanup

This migration guide ensures a safe, monitored, and reversible transition to the new high-performance CRM history system while maintaining business continuity and data integrity.
