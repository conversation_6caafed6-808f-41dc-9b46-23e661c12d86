using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Infrastructure.Data.Configurations;

/// <summary>
/// Entity Framework configuration for HistoryEntry.
/// Optimized for high-performance queries and storage efficiency.
/// </summary>
public class HistoryEntryConfiguration : IEntityTypeConfiguration<HistoryEntry>
{
    private readonly StorageTier _tier;

    public HistoryEntryConfiguration(StorageTier tier)
    {
        _tier = tier;
    }

    public void Configure(EntityTypeBuilder<HistoryEntry> builder)
    {
        // Primary key
        builder.HasKey(e => e.Id);
        builder.Property(e => e.Id)
               .ValueGeneratedOnAdd()
               .HasAnnotation("SqlServer:Identity", "1, 1");

        // Required fields
        builder.Property(e => e.LeadId)
               .IsRequired()
               .HasColumnType("int");

        builder.Property(e => e.FieldName)
               .IsRequired()
               .HasMaxLength(100)
               .HasColumnType("nvarchar(100)");

        builder.Property(e => e.ChangedBy)
               .IsRequired()
               .HasMaxLength(100)
               .HasColumnType("nvarchar(100)");

        builder.Property(e => e.ChangedAt)
               .IsRequired()
               .HasColumnType("datetime2(3)") // Precision to milliseconds
               .HasDefaultValueSql("GETUTCDATE()");

        // Optional fields with optimized storage
        builder.Property(e => e.OldValue)
               .HasMaxLength(4000)
               .HasColumnType("nvarchar(4000)");

        builder.Property(e => e.NewValue)
               .HasMaxLength(4000)
               .HasColumnType("nvarchar(4000)");

        builder.Property(e => e.Metadata)
               .HasMaxLength(2000)
               .HasColumnType("nvarchar(2000)");

        // Configure tier-specific optimizations
        ConfigureTierSpecificSettings(builder);

        // Add check constraints for data integrity
        builder.HasCheckConstraint("CK_HistoryEntry_LeadId", "[LeadId] > 0");
        builder.HasCheckConstraint("CK_HistoryEntry_FieldName", "LEN([FieldName]) > 0");
        builder.HasCheckConstraint("CK_HistoryEntry_ChangedBy", "LEN([ChangedBy]) > 0");
        builder.HasCheckConstraint("CK_HistoryEntry_Values", 
            "[OldValue] IS NOT NULL OR [NewValue] IS NOT NULL");
    }

    private void ConfigureTierSpecificSettings(EntityTypeBuilder<HistoryEntry> builder)
    {
        switch (_tier)
        {
            case StorageTier.Hot:
                ConfigureHotTierSettings(builder);
                break;
            case StorageTier.Warm:
                ConfigureWarmTierSettings(builder);
                break;
        }
    }

    private void ConfigureHotTierSettings(EntityTypeBuilder<HistoryEntry> builder)
    {
        // Hot tier: Optimized for maximum query performance
        
        // Clustered index on LeadId and ChangedAt for optimal range queries
        builder.HasIndex(e => new { e.LeadId, e.ChangedAt })
               .HasDatabaseName("IX_HistoryEntries_Hot_LeadId_ChangedAt_Clustered")
               .IsClustered()
               .IsDescending(false, true); // LeadId ASC, ChangedAt DESC

        // Non-clustered indexes for common query patterns
        builder.HasIndex(e => e.ChangedAt)
               .HasDatabaseName("IX_HistoryEntries_Hot_ChangedAt")
               .IsDescending(true)
               .HasFilter("[ChangedAt] >= DATEADD(day, -90, GETUTCDATE())"); // Only index recent data

        builder.HasIndex(e => new { e.FieldName, e.ChangedAt })
               .HasDatabaseName("IX_HistoryEntries_Hot_FieldName_ChangedAt")
               .IsDescending(false, true)
               .HasFilter("[ChangedAt] >= DATEADD(day, -90, GETUTCDATE())");

        builder.HasIndex(e => new { e.ChangedBy, e.ChangedAt })
               .HasDatabaseName("IX_HistoryEntries_Hot_ChangedBy_ChangedAt")
               .IsDescending(false, true)
               .HasFilter("[ChangedAt] >= DATEADD(day, -90, GETUTCDATE())");

        // Covering index for common SELECT scenarios
        builder.HasIndex(e => new { e.LeadId, e.FieldName, e.ChangedAt })
               .HasDatabaseName("IX_HistoryEntries_Hot_Covering")
               .IncludeProperties(e => new { e.OldValue, e.NewValue, e.ChangedBy })
               .IsDescending(false, false, true);

        // Memory-optimized table hint for SQL Server 2019+
        builder.HasAnnotation("SqlServer:MemoryOptimized", false); // Set to true if using In-Memory OLTP
        
        // No compression for hot tier to maximize speed
        builder.HasAnnotation("SqlServer:DataCompression", "None");
    }

    private void ConfigureWarmTierSettings(EntityTypeBuilder<HistoryEntry> builder)
    {
        // Warm tier: Balanced performance and storage efficiency
        
        // Clustered index optimized for date-based partitioning
        builder.HasIndex(e => new { e.ChangedAt, e.LeadId })
               .HasDatabaseName("IX_HistoryEntries_Warm_ChangedAt_LeadId_Clustered")
               .IsClustered()
               .IsDescending(true, false); // ChangedAt DESC, LeadId ASC

        // Essential non-clustered indexes
        builder.HasIndex(e => new { e.LeadId, e.ChangedAt })
               .HasDatabaseName("IX_HistoryEntries_Warm_LeadId_ChangedAt")
               .IsDescending(false, true);

        builder.HasIndex(e => e.FieldName)
               .HasDatabaseName("IX_HistoryEntries_Warm_FieldName");

        // Page compression for storage efficiency
        builder.HasAnnotation("SqlServer:DataCompression", "Page");

        // Partition scheme for date-based partitioning (would be configured at database level)
        builder.HasAnnotation("SqlServer:PartitionScheme", "PS_HistoryByMonth");
    }
}
