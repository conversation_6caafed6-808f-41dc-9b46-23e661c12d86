<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.7.2" />
    <PackageReference Include="xunit" Version="2.4.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="7.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="7.0.11" />
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="7.0.1" />
    <PackageReference Include="NBomber" Version="5.0.6" />
    <PackageReference Include="Testcontainers.SqlServer" Version="3.5.0" />
    <PackageReference Include="Testcontainers.Redis" Version="3.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\CrmHistorySystem.Core\CrmHistorySystem.Core.csproj" />
    <ProjectReference Include="..\..\src\CrmHistorySystem.Infrastructure\CrmHistorySystem.Infrastructure.csproj" />
    <ProjectReference Include="..\..\src\CrmHistorySystem.Api\CrmHistorySystem.Api.csproj" />
  </ItemGroup>

</Project>
