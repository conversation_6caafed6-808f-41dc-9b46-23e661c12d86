# Test Supabase Connection
Write-Host "🔍 Testing Supabase Connection" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

$supabaseUrl = "https://ezcwxlffzmqwdzpqxtan.supabase.co"
$supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV6Y3d4bGZmem1xd2R6cHF4dGFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1NzE5NTcsImV4cCI6MjA2ODE0Nzk1N30.1Zy3WPXbCRZs-piu5tW845EOQDSgxiJt8dizsn4Wi5M"

Write-Host "📡 Supabase URL: $supabaseUrl" -ForegroundColor Yellow
Write-Host "🔑 API Key: $($supabaseKey.Substring(0, 20))..." -ForegroundColor Yellow

try {
    Write-Host "🌐 Testing HTTP connection to Supabase..." -ForegroundColor Cyan
    
    $headers = @{
        "apikey" = $supabaseKey
        "Authorization" = "Bearer $supabaseKey"
        "Content-Type" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$supabaseUrl/rest/v1/" -Headers $headers -Method Get -TimeoutSec 10
    Write-Host "✅ Successfully connected to Supabase!" -ForegroundColor Green
    Write-Host "📊 Response received from Supabase API" -ForegroundColor Green
    
    # Test database connection
    Write-Host "🗄️ Testing database tables..." -ForegroundColor Cyan
    try {
        $tablesResponse = Invoke-RestMethod -Uri "$supabaseUrl/rest/v1/leads?limit=1" -Headers $headers -Method Get -TimeoutSec 10
        Write-Host "✅ Successfully queried leads table!" -ForegroundColor Green
        Write-Host "📋 Found $($tablesResponse.Count) records in leads table" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️ Leads table doesn't exist yet - this is expected for a new setup" -ForegroundColor Yellow
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    Write-Host "" -ForegroundColor White
    Write-Host "🎉 Supabase connection test completed!" -ForegroundColor Green
    Write-Host "✅ Your Supabase instance is accessible and ready to use" -ForegroundColor Green
    Write-Host "" -ForegroundColor White
    Write-Host "📝 Next steps:" -ForegroundColor Cyan
    Write-Host "   1. The CRM History System can connect to your Supabase database" -ForegroundColor White
    Write-Host "   2. Tables will be created automatically when the application starts" -ForegroundColor White
    Write-Host "   3. You can now run the CRM History System demo" -ForegroundColor White
    
}
catch {
    Write-Host "❌ Failed to connect to Supabase" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "" -ForegroundColor White
    Write-Host "🔧 Troubleshooting:" -ForegroundColor Yellow
    Write-Host "   1. Check your internet connection" -ForegroundColor White
    Write-Host "   2. Verify the Supabase URL and API key are correct" -ForegroundColor White
    Write-Host "   3. Ensure your Supabase project is active" -ForegroundColor White
}

Write-Host "" -ForegroundColor White
Write-Host "🔗 Supabase Dashboard: https://app.supabase.com/project/ezcwxlffzmqwdzpqxtan" -ForegroundColor Cyan
