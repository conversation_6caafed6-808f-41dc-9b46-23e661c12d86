using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Azure.Storage.Blobs;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Services;
using CrmHistorySystem.Core.Configuration;
using CrmHistorySystem.Infrastructure.Data;
using CrmHistorySystem.Infrastructure.Services;
using CrmHistorySystem.Infrastructure.Storage;
using CrmHistorySystem.Infrastructure.Caching;
using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Infrastructure.DependencyInjection;

/// <summary>
/// Extension methods for configuring CRM History System services in dependency injection.
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds all CRM History System services to the dependency injection container.
    /// </summary>
    public static IServiceCollection AddCrmHistorySystem(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Configure options
        services.Configure<HistoryOptions>(configuration.GetSection(HistoryOptions.SectionName));

        // Add core services
        services.AddScoped<IHistoryService, TieredHistoryService>();
        services.AddScoped<ILeadHistoryService, LeadHistoryService>();
        services.AddSingleton<IHistoryCache, RedisHistoryCache>();

        // Add storage implementations
        services.AddTierStorages(configuration);

        // Add database contexts
        services.AddDatabaseContexts(configuration);

        // Add caching
        services.AddHistoryCaching(configuration);

        // Add background services
        services.AddBackgroundServices();

        return services;
    }

    /// <summary>
    /// Adds tier storage implementations.
    /// </summary>
    private static IServiceCollection AddTierStorages(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var historyOptions = configuration.GetSection(HistoryOptions.SectionName).Get<HistoryOptions>()
            ?? throw new InvalidOperationException("History configuration is required");

        // Hot tier storage (SQL Server)
        services.AddScoped<ITierStorage>(provider =>
        {
            var context = provider.GetRequiredService<HistoryDbContext>();
            var logger = provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<SqlServerTierStorage>>();
            var options = provider.GetRequiredService<Microsoft.Extensions.Options.IOptions<HistoryOptions>>();
            
            // Create context specifically for hot tier
            var hotContext = CreateTierContext(provider, StorageTier.Hot, historyOptions.ConnectionStrings.Hot);
            return new SqlServerTierStorage(hotContext, logger, options);
        });

        // Warm tier storage (SQL Server with compression)
        services.AddScoped<ITierStorage>(provider =>
        {
            var logger = provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<SqlServerTierStorage>>();
            var options = provider.GetRequiredService<Microsoft.Extensions.Options.IOptions<HistoryOptions>>();
            
            // Create context specifically for warm tier
            var warmContext = CreateTierContext(provider, StorageTier.Warm, historyOptions.ConnectionStrings.Warm);
            return new SqlServerTierStorage(warmContext, logger, options);
        });

        // Cold tier storage (Azure Blob Storage)
        services.AddScoped<ITierStorage>(provider =>
        {
            var blobServiceClient = new BlobServiceClient(historyOptions.ConnectionStrings.Cold);
            var logger = provider.GetRequiredService<Microsoft.Extensions.Logging.ILogger<BlobStorageTierStorage>>();
            var options = provider.GetRequiredService<Microsoft.Extensions.Options.IOptions<HistoryOptions>>();
            
            return new BlobStorageTierStorage(blobServiceClient, logger, options);
        });

        return services;
    }

    /// <summary>
    /// Adds database contexts for SQL Server tiers.
    /// </summary>
    private static IServiceCollection AddDatabaseContexts(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var historyOptions = configuration.GetSection(HistoryOptions.SectionName).Get<HistoryOptions>()
            ?? throw new InvalidOperationException("History configuration is required");

        // Primary context (can be used for hot tier by default)
        services.AddDbContext<HistoryDbContext>(options =>
        {
            options.UseSqlServer(historyOptions.ConnectionStrings.Hot, sqlOptions =>
            {
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: historyOptions.MaxRetryAttempts,
                    maxRetryDelay: TimeSpan.FromMilliseconds(historyOptions.MaxRetryDelayMs),
                    errorNumbersToAdd: null);
                
                sqlOptions.CommandTimeout(historyOptions.QueryTimeoutSeconds);
            });

            options.EnableServiceProviderCaching();
            options.EnableSensitiveDataLogging(false);
        });

        return services;
    }

    /// <summary>
    /// Adds caching services.
    /// </summary>
    private static IServiceCollection AddHistoryCaching(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var historyOptions = configuration.GetSection(HistoryOptions.SectionName).Get<HistoryOptions>()
            ?? throw new InvalidOperationException("History configuration is required");

        if (historyOptions.Cache.UseDistributedCache && !string.IsNullOrEmpty(historyOptions.ConnectionStrings.Redis))
        {
            // Add Redis distributed cache
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = historyOptions.ConnectionStrings.Redis;
                options.InstanceName = "CrmHistory";
            });
        }
        else
        {
            // Fallback to in-memory cache
            services.AddMemoryCache();
            services.AddSingleton<Microsoft.Extensions.Caching.Distributed.IDistributedCache, 
                Microsoft.Extensions.Caching.Memory.MemoryDistributedCache>();
        }

        return services;
    }

    /// <summary>
    /// Adds background services for maintenance tasks.
    /// </summary>
    private static IServiceCollection AddBackgroundServices(this IServiceCollection services)
    {
        // Add background service for automatic archival
        services.AddHostedService<ArchivalBackgroundService>();

        // Add background service for cache cleanup
        services.AddHostedService<CacheCleanupBackgroundService>();

        return services;
    }

    /// <summary>
    /// Creates a database context for a specific tier.
    /// </summary>
    private static HistoryDbContext CreateTierContext(
        IServiceProvider provider,
        StorageTier tier,
        string connectionString)
    {
        var optionsBuilder = new DbContextOptionsBuilder<HistoryDbContext>();
        
        optionsBuilder.UseSqlServer(connectionString, sqlOptions =>
        {
            sqlOptions.EnableRetryOnFailure(maxRetryCount: 3, maxRetryDelay: TimeSpan.FromSeconds(5), errorNumbersToAdd: null);
            sqlOptions.CommandTimeout(30);
        });

        optionsBuilder.EnableServiceProviderCaching();
        optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);

        return new HistoryDbContext(optionsBuilder.Options, tier);
    }

    /// <summary>
    /// Validates the configuration and ensures all required services are properly configured.
    /// </summary>
    public static IServiceCollection ValidateHistoryConfiguration(this IServiceCollection services)
    {
        // Add a configuration validator that runs at startup
        services.AddSingleton<IStartupFilter, HistoryConfigurationValidator>();
        return services;
    }
}

/// <summary>
/// Background service for automatic archival of old history entries.
/// </summary>
public class ArchivalBackgroundService : Microsoft.Extensions.Hosting.BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly Microsoft.Extensions.Logging.ILogger<ArchivalBackgroundService> _logger;
    private readonly HistoryOptions _options;

    public ArchivalBackgroundService(
        IServiceProvider serviceProvider,
        Microsoft.Extensions.Logging.ILogger<ArchivalBackgroundService> logger,
        Microsoft.Extensions.Options.IOptions<HistoryOptions> options)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_options.Archival.AutoArchivalEnabled)
        {
            _logger.LogInformation("Automatic archival is disabled");
            return;
        }

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var historyService = scope.ServiceProvider.GetRequiredService<IHistoryService>();

                var cutoffDate = DateTime.UtcNow.AddDays(-_options.HotTierRetentionDays);
                var archivedCount = await historyService.ArchiveOldEntriesAsync(cutoffDate, stoppingToken);

                if (archivedCount > 0)
                {
                    _logger.LogInformation("Archived {Count} history entries", archivedCount);
                }

                // Wait 24 hours before next archival run
                await Task.Delay(TimeSpan.FromHours(24), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during automatic archival");
                await Task.Delay(TimeSpan.FromHours(1), stoppingToken); // Retry in 1 hour
            }
        }
    }
}

/// <summary>
/// Background service for cache cleanup and maintenance.
/// </summary>
public class CacheCleanupBackgroundService : Microsoft.Extensions.Hosting.BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly Microsoft.Extensions.Logging.ILogger<CacheCleanupBackgroundService> _logger;

    public CacheCleanupBackgroundService(
        IServiceProvider serviceProvider,
        Microsoft.Extensions.Logging.ILogger<CacheCleanupBackgroundService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var cache = scope.ServiceProvider.GetRequiredService<IHistoryCache>();

                // Perform cache maintenance (this would be implemented in the cache service)
                _logger.LogDebug("Performing cache maintenance");

                // Wait 1 hour before next cleanup
                await Task.Delay(TimeSpan.FromHours(1), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cache cleanup");
                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken);
            }
        }
    }
}

/// <summary>
/// Startup filter to validate configuration.
/// </summary>
public class HistoryConfigurationValidator : IStartupFilter
{
    private readonly HistoryOptions _options;
    private readonly Microsoft.Extensions.Logging.ILogger<HistoryConfigurationValidator> _logger;

    public HistoryConfigurationValidator(
        Microsoft.Extensions.Options.IOptions<HistoryOptions> options,
        Microsoft.Extensions.Logging.ILogger<HistoryConfigurationValidator> logger)
    {
        _options = options.Value;
        _logger = logger;
    }

    public Action<Microsoft.AspNetCore.Builder.IApplicationBuilder> Configure(Action<Microsoft.AspNetCore.Builder.IApplicationBuilder> next)
    {
        return app =>
        {
            ValidateConfiguration();
            next(app);
        };
    }

    private void ValidateConfiguration()
    {
        if (!_options.IsValid(out var errors))
        {
            var errorMessage = $"Invalid History configuration: {string.Join(", ", errors)}";
            _logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        _logger.LogInformation("History configuration validated successfully");
    }
}
