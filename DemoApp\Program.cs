using Microsoft.EntityFrameworkCore;
using DemoApp.Data;
using DemoApp.Services;
using Supabase;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();

// Add Entity Framework with Supabase PostgreSQL
builder.Services.AddDbContext<DemoDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("DefaultConnection")));

// Add Supabase client
var supabaseUrl = builder.Configuration["Supabase:Url"];
var supabaseKey = builder.Configuration["Supabase:Key"];
builder.Services.AddSingleton(provider => new Client(supabaseUrl, supabaseKey));

// Add our services
builder.Services.AddScoped<LeadHistoryService>();

// Add API versioning
builder.Services.AddApiVersioning(opt =>
{
    opt.DefaultApiVersion = new Microsoft.AspNetCore.Mvc.ApiVersion(1, 0);
    opt.AssumeDefaultVersionWhenUnspecified = true;
    opt.ApiVersionReader = Microsoft.AspNetCore.Mvc.ApiVersioning.ApiVersionReader.Combine(
        new Microsoft.AspNetCore.Mvc.ApiVersioning.QueryStringApiVersionReader("version"),
        new Microsoft.AspNetCore.Mvc.ApiVersioning.HeaderApiVersionReader("X-Version"),
        new Microsoft.AspNetCore.Mvc.ApiVersioning.MediaTypeApiVersionReader("ver")
    );
});

builder.Services.AddVersionedApiExplorer(setup =>
{
    setup.GroupNameFormat = "'v'VVV";
    setup.SubstituteApiVersionInUrl = true;
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "CRM History System Demo API",
        Version = "v1",
        Description = "Demonstration of the CRM History System with automatic lead change tracking"
    });
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "CRM History System Demo API v1");
        c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
    });
}

app.UseCors();
app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();

// Initialize database with sample data
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<DemoDbContext>();
    var historyService = scope.ServiceProvider.GetRequiredService<LeadHistoryService>();
    
    try
    {
        // Ensure database is created
        await context.Database.EnsureCreatedAsync();
        
        // Add sample data if database is empty
        if (!await context.Leads.AnyAsync())
        {
            var sampleLeads = new[]
            {
                new DemoApp.Models.Lead
                {
                    Name = "John Doe",
                    ContactNo = "**********",
                    Email = "<EMAIL>",
                    Notes = "Interested in premium properties",
                    ChosenProject = "Luxury Apartments",
                    AssignTo = Guid.NewGuid(),
                    CreatedBy = "system",
                    UpdatedBy = "system"
                },
                new DemoApp.Models.Lead
                {
                    Name = "Jane Smith",
                    ContactNo = "**********",
                    Email = "<EMAIL>",
                    Notes = "Looking for 3BHK apartment",
                    ChosenProject = "Premium Villas",
                    AssignTo = Guid.NewGuid(),
                    CreatedBy = "system",
                    UpdatedBy = "system"
                }
            };

            context.Leads.AddRange(sampleLeads);
            await context.SaveChangesAsync();

            // Record creation history for sample leads
            foreach (var lead in sampleLeads)
            {
                await historyService.RecordLeadCreationAsync(lead, "system");
            }

            Console.WriteLine("Sample data created with history tracking!");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Error initializing database: {ex.Message}");
        // Continue running the app even if database initialization fails
    }
}

Console.WriteLine("🚀 CRM History System Demo API is running!");
Console.WriteLine("📖 Swagger UI available at: http://localhost:5000");
Console.WriteLine("🔍 Try the following endpoints:");
Console.WriteLine("   GET  /api/leads - Get all leads");
Console.WriteLine("   POST /api/leads - Create a new lead");
Console.WriteLine("   GET  /api/leads/{id}/history - Get lead history");
Console.WriteLine("   GET  /api/leads/history/stats - Get storage statistics");

app.Run();
