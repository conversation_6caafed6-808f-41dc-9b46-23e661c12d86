using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Core.Services;
using CrmHistorySystem.Infrastructure.DependencyInjection;

namespace CrmHistorySystem.Examples;

/// <summary>
/// Example demonstrating how to use the Lead History Service
/// for tracking lead creation and modifications.
/// </summary>
public class LeadHistoryExample
{
    private readonly ILeadHistoryService _leadHistoryService;
    private readonly ILogger<LeadHistoryExample> _logger;

    public LeadHistoryExample(ILeadHistoryService leadHistoryService, ILogger<LeadHistoryExample> logger)
    {
        _leadHistoryService = leadHistoryService;
        _logger = logger;
    }

    /// <summary>
    /// Demonstrates recording lead creation history.
    /// </summary>
    public async Task DemonstrateLeadCreationAsync()
    {
        Console.WriteLine("=== Lead Creation History Example ===");

        // Create a new lead
        var newLead = new Lead
        {
            Id = 12345,
            Name = "<PERSON>",
            ContactNo = "1234567890",
            AlternateContactNo = "0987654321",
            Email = "<EMAIL>",
            Notes = "Interested in premium properties",
            ScheduledDate = DateTime.UtcNow.AddDays(7),
            ChosenProject = "Luxury Apartments",
            AssignTo = Guid.NewGuid(),
            ShareCount = 0,
            CreatedBy = "sales-agent-1"
        };

        // Record the lead creation in history
        var success = await _leadHistoryService.RecordLeadCreationAsync(newLead, "sales-agent-1");
        
        if (success)
        {
            Console.WriteLine($"✅ Successfully recorded creation history for lead {newLead.Id}");
            
            // Retrieve and display the history
            var history = await _leadHistoryService.GetLeadHistoryAsync(newLead.Id, pageSize: 20);
            Console.WriteLine($"📊 Created {history.TotalCount} history entries:");
            
            foreach (var entry in history.Data)
            {
                Console.WriteLine($"   • {entry.FieldName}: null → '{entry.NewValue}'");
            }
        }
        else
        {
            Console.WriteLine("❌ Failed to record creation history");
        }
    }

    /// <summary>
    /// Demonstrates recording lead modification history.
    /// </summary>
    public async Task DemonstrateLeadModificationAsync()
    {
        Console.WriteLine("\n=== Lead Modification History Example ===");

        // Original lead state
        var originalLead = new Lead
        {
            Id = 12345,
            Name = "John Doe",
            ContactNo = "1234567890",
            Email = "<EMAIL>",
            Notes = "Interested in premium properties",
            ChosenProject = "Luxury Apartments"
        };

        // Modified lead state
        var modifiedLead = new Lead
        {
            Id = 12345,
            Name = "John Doe",
            ContactNo = "1234567890",
            Email = "<EMAIL>", // Changed
            Notes = "Very interested in premium properties. Prefers 3BHK.", // Changed
            ChosenProject = "Premium Villas" // Changed
        };

        // Record the changes
        var success = await _leadHistoryService.RecordLeadChangesAsync(
            12345, originalLead, modifiedLead, "sales-agent-2");

        if (success)
        {
            Console.WriteLine("✅ Successfully recorded modification history");
            
            // Retrieve recent changes
            var recentHistory = await _leadHistoryService.GetLeadHistoryAsync(
                12345, 
                startDate: DateTime.UtcNow.AddMinutes(-5),
                pageSize: 10);
            
            Console.WriteLine($"📊 Recent changes ({recentHistory.CurrentPageCount} entries):");
            foreach (var entry in recentHistory.Data)
            {
                Console.WriteLine($"   • {entry.FieldName}: '{entry.OldValue}' → '{entry.NewValue}' by {entry.ChangedBy}");
            }
        }
        else
        {
            Console.WriteLine("❌ Failed to record modification history");
        }
    }

    /// <summary>
    /// Demonstrates recording individual field changes.
    /// </summary>
    public async Task DemonstrateFieldChangeAsync()
    {
        Console.WriteLine("\n=== Individual Field Change Example ===");

        var leadId = 12345;
        
        // Record a status change
        var success = await _leadHistoryService.RecordFieldChangeAsync(
            leadId: leadId,
            fieldName: "Status",
            oldValue: "New",
            newValue: "Contacted",
            changedBy: "sales-agent-1",
            metadata: "{\"contactMethod\": \"phone\", \"duration\": \"15 minutes\"}"
        );

        if (success)
        {
            Console.WriteLine("✅ Successfully recorded status change");
        }

        // Record another change
        await _leadHistoryService.RecordFieldChangeAsync(
            leadId: leadId,
            fieldName: "Priority",
            oldValue: "Medium",
            newValue: "High",
            changedBy: "sales-manager",
            metadata: "{\"reason\": \"High-value prospect\"}"
        );

        // Show filtered history for Status field only
        var statusHistory = await _leadHistoryService.GetLeadHistoryAsync(
            leadId, fieldName: "Status", pageSize: 5);
        
        Console.WriteLine($"📊 Status change history ({statusHistory.CurrentPageCount} entries):");
        foreach (var entry in statusHistory.Data)
        {
            Console.WriteLine($"   • {entry.ChangedAt:yyyy-MM-dd HH:mm}: {entry.OldValue} → {entry.NewValue} by {entry.ChangedBy}");
        }
    }

    /// <summary>
    /// Demonstrates batch recording of multiple changes.
    /// </summary>
    public async Task DemonstrateBatchChangesAsync()
    {
        Console.WriteLine("\n=== Batch Changes Example ===");

        var leadId = 12345;
        var changes = new[]
        {
            FieldChange.ForModification("Email", "<EMAIL>", "<EMAIL>"),
            FieldChange.ForModification("Priority", "High", "Critical"),
            FieldChange.ForCreation("LastContactDate", DateTime.UtcNow.ToString("yyyy-MM-dd")),
            FieldChange.ForCreation("NextFollowUp", DateTime.UtcNow.AddDays(3).ToString("yyyy-MM-dd"))
        };

        var result = await _leadHistoryService.RecordBatchChangesAsync(
            leadId, 
            changes, 
            "sales-agent-3", 
            "{\"operation\": \"qualification-update\", \"source\": \"crm-mobile-app\"}"
        );

        Console.WriteLine($"📊 Batch operation result:");
        Console.WriteLine($"   • Batch ID: {result.BatchId}");
        Console.WriteLine($"   • Success: {result.IsSuccess}");
        Console.WriteLine($"   • Successful: {result.SuccessCount}/{result.TotalCount}");
        Console.WriteLine($"   • Processing time: {result.ProcessingTimeMs}ms");

        if (result.Errors.Any())
        {
            Console.WriteLine($"   • Errors: {string.Join(", ", result.Errors)}");
        }
    }

    /// <summary>
    /// Demonstrates querying lead history with various filters.
    /// </summary>
    public async Task DemonstrateHistoryQueryingAsync()
    {
        Console.WriteLine("\n=== History Querying Example ===");

        var leadId = 12345;

        // Get all history
        var allHistory = await _leadHistoryService.GetLeadHistoryAsync(leadId, pageSize: 50);
        Console.WriteLine($"📊 Total history entries: {allHistory.TotalCount}");

        // Get history for last 24 hours
        var recentHistory = await _leadHistoryService.GetLeadHistoryAsync(
            leadId,
            startDate: DateTime.UtcNow.AddDays(-1),
            pageSize: 20
        );
        Console.WriteLine($"📊 Last 24 hours: {recentHistory.CurrentPageCount} entries");

        // Get email-related changes only
        var emailHistory = await _leadHistoryService.GetLeadHistoryAsync(
            leadId,
            fieldName: "Email",
            pageSize: 10
        );
        Console.WriteLine($"📊 Email changes: {emailHistory.CurrentPageCount} entries");

        // Display recent changes with details
        Console.WriteLine("\n🕒 Recent changes:");
        foreach (var entry in recentHistory.Data.Take(5))
        {
            var changeType = entry.OldValue == null ? "CREATED" : "MODIFIED";
            Console.WriteLine($"   • [{changeType}] {entry.FieldName} at {entry.ChangedAt:HH:mm:ss} by {entry.ChangedBy}");
            
            if (entry.OldValue != null)
            {
                Console.WriteLine($"     Old: {entry.OldValue}");
            }
            Console.WriteLine($"     New: {entry.NewValue}");
            
            if (!string.IsNullOrEmpty(entry.Metadata))
            {
                Console.WriteLine($"     Metadata: {entry.Metadata}");
            }
        }
    }

    /// <summary>
    /// Main method to run all examples.
    /// </summary>
    public static async Task Main(string[] args)
    {
        // Setup dependency injection
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole());
        
        // Add configuration (you would load this from appsettings.json in a real app)
        var configuration = new Microsoft.Extensions.Configuration.ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string>
            {
                ["History:HotTierRetentionDays"] = "90",
                ["History:WarmTierRetentionDays"] = "365",
                ["ConnectionStrings:Hot"] = "Server=(localdb)\\mssqllocaldb;Database=CrmHistory_Hot;Trusted_Connection=true",
                ["ConnectionStrings:Warm"] = "Server=(localdb)\\mssqllocaldb;Database=CrmHistory_Warm;Trusted_Connection=true",
                ["ConnectionStrings:Cold"] = "UseDevelopmentStorage=true",
                ["ConnectionStrings:Redis"] = "localhost:6379"
            })
            .Build();
        
        // Add CRM History System
        services.AddCrmHistorySystem(configuration);
        
        // Build service provider
        var serviceProvider = services.BuildServiceProvider();
        
        // Create example instance
        var example = new LeadHistoryExample(
            serviceProvider.GetRequiredService<ILeadHistoryService>(),
            serviceProvider.GetRequiredService<ILogger<LeadHistoryExample>>()
        );

        try
        {
            // Run all examples
            await example.DemonstrateLeadCreationAsync();
            await example.DemonstrateLeadModificationAsync();
            await example.DemonstrateFieldChangeAsync();
            await example.DemonstrateBatchChangesAsync();
            await example.DemonstrateHistoryQueryingAsync();
            
            Console.WriteLine("\n✅ All examples completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ Error running examples: {ex.Message}");
            Console.WriteLine(ex.StackTrace);
        }
        finally
        {
            serviceProvider.Dispose();
        }
    }
}
